import { z } from "zod";

// Budget slider constants
export const BUDGET_CONFIG = {
    MIN: 300,
    MAX: 10000,
    STEP: 100,
    DEFAULT: 300,
} as const;

// Form validation schema with numeric budget
export const searchSchema = z.object({
    interests: z.string().min(1, "Please select an interest"),
    date: z
        .object({
            from: z.date().optional(),
            to: z.date().optional(),
        })
        .optional(),
    budget: z.number().min(BUDGET_CONFIG.MIN).max(BUDGET_CONFIG.MAX),
});
