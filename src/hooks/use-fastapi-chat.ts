// src/hooks/use-fastapi-chat.ts

import { useState, useCallback, useRef } from "react";
import { v4 as uuidv4 } from "uuid";
import {
    Message,
    ChatState,
    ChatHookOptions,
    ChatHookReturn,
} from "../../types/chat";
import { CHAT_CONSTANTS } from "@/constants/chat";
import { handleQuery } from "@/app/fastapi-actions";
import { useToast } from "@/hooks/use-toast";

/**
 * FastAPI chat message format for backend communication
 */
interface FastAPIChatMessage {
    role: "user" | "assistant";
    parts: Array<{ text: string }>;
}

/**
 * Enhanced custom hook for managing chat messages using FastAPI backend
 *
 * **Drop-in replacement for useChatMessages:**
 * - Same interface and return type as the original useChatMessages hook
 * - Uses FastAPI backend instead of Genkit for AI processing
 * - Handles message format conversion between internal and FastAPI formats
 * - Maintains compatibility with existing chat components
 *
 * **Key differences from useChatMessages:**
 * - Uses HTTP requests to FastAPI backend instead of Genkit server actions
 * - Converts message history to FastAPI format (role + parts structure)
 * - Enhanced timeout handling and error recovery
 * - Network-aware error messages for better user experience
 *
 * **Usage:**
 * ```typescript
 * // Replace this:
 * import { useChatMessages } from "@/hooks/use-chat-messages";
 * const { messages, isLoading, sendMessage } = useChatMessages();
 *
 * // With this:
 * import { useChatMessages } from "@/hooks/use-fastapi-chat";
 * const { messages, isLoading, sendMessage } = useChatMessages();
 * ```
 *
 * @param options - Optional configuration for the chat hook
 * @returns Chat state and methods for interaction (same as useChatMessages)
 */
export function useChatMessages(options: ChatHookOptions = {}): ChatHookReturn {
    const {
        initialMessage = CHAT_CONSTANTS.INITIAL_MESSAGE,
        maxRetries = CHAT_CONSTANTS.MAX_RETRY_ATTEMPTS,
        enableRetry = true,
    } = options;

    const { toast } = useToast();
    const retryCountRef = useRef(0);

    const [chatState, setChatState] = useState<ChatState>({
        messages: [
            {
                id: uuidv4(),
                role: "assistant",
                content: initialMessage,
                timestamp: new Date(),
            },
        ],
        isLoading: false,
        error: null,
    });

    /**
     * Converts internal message format to FastAPI format
     * @param messages - Array of internal messages
     * @returns Array of FastAPI formatted messages
     */
    const convertToFastAPIHistory = useCallback(
        (messages: Message[]): FastAPIChatMessage[] => {
            return messages.map((msg) => ({
                role: msg.role,
                parts: [{ text: msg.content }],
            }));
        },
        [],
    );

    /**
     * Validates message input according to chat constants
     * @param message - The message to validate
     * @returns Error message if invalid, null if valid
     */
    const validateMessage = useCallback((message: string): string | null => {
        if (!message.trim()) {
            return CHAT_CONSTANTS.ERROR_MESSAGES.VALIDATION;
        }
        if (message.length > CHAT_CONSTANTS.MAX_MESSAGE_LENGTH) {
            return `Message too long. Maximum ${CHAT_CONSTANTS.MAX_MESSAGE_LENGTH} characters allowed.`;
        }
        return null;
    }, []);

    /**
     * Adds a new message to the chat state
     * @param role - The role of the message sender
     * @param content - The message content
     * @returns The created message object
     */
    const addMessage = useCallback((role: Message["role"], content: string) => {
        const newMessage: Message = {
            id: uuidv4(),
            role,
            content,
            timestamp: new Date(),
        };

        setChatState((prev) => ({
            ...prev,
            messages: [
                ...prev.messages.slice(-CHAT_CONSTANTS.MAX_MESSAGES + 1),
                newMessage,
            ],
            error: null,
        }));

        return newMessage;
    }, []);

    /**
     * Sends a message to the FastAPI backend and handles the response
     * @param userInput - The user's message
     */
    const sendMessage = useCallback(
        async (userInput: string): Promise<void> => {
            // Validate input
            const validationError = validateMessage(userInput);
            if (validationError) {
                toast({
                    variant: "destructive",
                    title: "Invalid Input",
                    description: validationError,
                });
                return;
            }

            // Add user message
            addMessage("user", userInput);

            // Set loading state
            setChatState((prev) => ({ ...prev, isLoading: true, error: null }));

            try {
                // Convert current messages to FastAPI format (excluding the new user message from history)
                const history = convertToFastAPIHistory([
                    ...chatState.messages,
                ]);

                const response = await handleQuery({
                    query: userInput,
                    history: history.slice(0, -1), // Exclude the current user message from history
                });

                addMessage("assistant", response);
                retryCountRef.current = 0; // Reset retry count on success
            } catch (error) {
                console.error("FastAPI Chat Error:", error);

                // Determine error type and message
                let errorMessage: string =
                    CHAT_CONSTANTS.ERROR_MESSAGES.GENERIC;
                if (error instanceof Error) {
                    if (
                        error.message.includes("network") ||
                        error.message.includes("fetch") ||
                        error.message.includes("timeout")
                    ) {
                        errorMessage = CHAT_CONSTANTS.ERROR_MESSAGES.NETWORK;
                    } else if (error.message.includes("rate limit")) {
                        errorMessage = CHAT_CONSTANTS.ERROR_MESSAGES.RATE_LIMIT;
                    }
                }

                addMessage("assistant", errorMessage);

                setChatState((prev) => ({
                    ...prev,
                    error:
                        error instanceof Error
                            ? error.message
                            : "Unknown error",
                }));

                toast({
                    variant: "destructive",
                    title: "AI Error",
                    description: errorMessage,
                });
            } finally {
                setChatState((prev) => ({ ...prev, isLoading: false }));
            }
        },
        [
            addMessage,
            validateMessage,
            toast,
            chatState.messages,
            convertToFastAPIHistory,
        ],
    );

    /**
     * Retries the last user message with configurable options
     */
    const retryLastMessage = useCallback(() => {
        if (!enableRetry) {
            toast({
                variant: "destructive",
                title: "Retry Disabled",
                description: "Retry functionality is not enabled.",
            });
            return;
        }

        if (retryCountRef.current >= maxRetries) {
            toast({
                variant: "destructive",
                title: "Max Retries Reached",
                description: "Please try again later.",
            });
            return;
        }

        const lastUserMessage = chatState.messages
            .slice()
            .reverse()
            .find((msg) => msg.role === "user");

        if (lastUserMessage) {
            retryCountRef.current += 1;
            sendMessage(lastUserMessage.content);
        }
    }, [chatState.messages, sendMessage, toast, enableRetry, maxRetries]);

    return {
        messages: chatState.messages,
        isLoading: chatState.isLoading,
        error: chatState.error,
        sendMessage,
        ...(enableRetry && { retryLastMessage }),
    };
}
