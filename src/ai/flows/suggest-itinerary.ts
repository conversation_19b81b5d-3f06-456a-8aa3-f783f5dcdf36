"use server";

/**
 * @fileOverview This file defines a Genkit flow for suggesting Bali travel itineraries based on user preferences.
 *
 * - suggestItinerary - A function that takes user interests, travel dates, and budget as input and returns a suggested itinerary.
 * - SuggestItineraryInput - The input type for the suggestItinerary function.
 * - SuggestItineraryOutput - The return type for the suggestItinerary function.
 */

import { ai } from "@/ai/genkit";
import { WHATSAPP_NUMBER } from "@/lib/config";
import { z } from "genkit";

const SuggestItineraryInputSchema = z.object({
    interests: z
        .string()
        .describe(
            "A comma-separated list of interests, e.g., beaches, temples, surfing.",
        ),
    travelDates: z
        .string()
        .describe(
            "The desired travel dates, e.g., 2024-07-01 to 2024-07-15. Use ISO 8601 format.",
        ),
    budget: z
        .string()
        .describe(
            "The budget for the trip, e.g., $1000, €800, or specify a range like $800-$1200.",
        ),
});

export type SuggestItineraryInput = z.infer<typeof SuggestItineraryInputSchema>;

const SuggestItineraryOutputSchema = z.object({
    itinerary: z.string().describe("A detailed travel itinerary for Bali."),
});

export type SuggestItineraryOutput = z.infer<
    typeof SuggestItineraryOutputSchema
>;

export async function suggestItinerary(
    input: SuggestItineraryInput,
): Promise<SuggestItineraryOutput> {
    return suggestItineraryFlow(input);
}

const prompt = ai.definePrompt({
    name: "suggestItineraryPrompt",
    input: {
        schema: z.object({
            ...SuggestItineraryInputSchema.shape,
            whatsappNumber: z.string(),
        }),
    },
    output: { schema: SuggestItineraryOutputSchema },
    prompt: `You are an expert travel planner specializing in authentic Bali experiences. Create a comprehensive and engaging travel itinerary for Bali based on user preferences.

            **Traveler Profile:**
            🎯 **Interests:** {{{interests}}}
            📅 **Travel Dates:** {{{travelDates}}}
            💰 **Budget:** {{{budget}}}

            **Create a Complete Itinerary That Includes:**

            🌅 **DAILY BREAKDOWN**
            Provide detailed day-by-day activities with:
            - **Morning Activities** (9-11 AM) with specific locations and times
            - **Afternoon Activities** (12-4 PM) with lunch suggestions
            - **Evening Activities** (5-8 PM) with dinner recommendations
            - **Evening Wind-down** (relaxation/beverages options)

            🏨 **HANDPICKED ACCOMMODATIONS**
            - 2-3 specific hotel/villa recommendations with price ranges
            - Location details and why they suit the traveler's interests
            - Include both mid-range and premium options within budget

            🍜 **CULINARY EXPERIENCES**
            - Daily restaurant recommendations for breakfast, lunch, dinner
            - Must-try Balinese dishes with local specialties
            - Food market or cooking class suggestions
            - Beverage recommendations (non-alcoholic where appropriate)

            🚗 **TRANSPORTATION & LOGISTICS**
            - Getting to/from airport
            - Daily transportation options (cars, scooters, taxis)
            - Estimated costs and booking tips

            🎭 **CULTURAL IMMERSION**
            - Local customs and etiquette tips
            - Temple visit protocols if applicable
            - Language basics and communication tips
            - Respectful photography guidelines

            💸 **COMPREHENSIVE BUDGET BREAKDOWN**
            - Daily spending estimates
            - Total estimated cost vs. declared budget
            - Cost-saving tips and premium upgrade options

            ⚡ **PRACTICAL INFORMATION**
            - Best times to visit featured locations
            - Weather considerations for travel dates
            - Health and safety tips
            - Emergency contacts

            ⭐ **AUTHENTIC EXPERIENCES**
            - Unique local experiences not found in guidebooks
            - Behind-the-scenes access opportunities
            - Meet locals and community interactions
            - Hidden gems based on specific interests

            **FORMATTING REQUIREMENTS:**
            - Use emojis liberally to make content engaging and easy to scan
            - ADD MULTIPLE EMPTY LINES (3-4) between major sections to create clear visual separation
            - ADD EMPTY LINES (1-2) between subsections and paragraphs for better readability
            - Use markdown headers (##, ###) for clear organization with consistent spacing
            - Include bullet points and numbered lists for readability
            - Use **bold text** for important information
            - Ensure each day has substantial content (at least 300-400 words each)
            - Total itinerary should provide complete coverage for the travel dates
            - DO NOT cram content together - always add breathing room between sections

            **CONTENT QUALITY:**
            - Focus on authentic, immersive Balinese experiences
            - Consider safety, sustainability, and cultural respect
            - Make the itinerary exciting and inspirational
            - Provide specific addresses, timing, and cost estimates where possible
            - Tailor all recommendations to the traveler's specific interests and budget

            At the end of the itinerary, ALWAYS include this note:
            ---
            **🌟 A Friendly Note:** This itinerary is a great starting point, but remember that details like opening hours and prices can change. We recommend double-checking before you go! For the most up-to-date information and to customize this plan with one of our experts, please contact us on WhatsApp at +{{{whatsappNumber}}}. We'd love to help you create the perfect Bali journey!
            ---
            `,
});

const suggestItineraryFlow = ai.defineFlow(
    {
        name: "suggestItineraryFlow",
        inputSchema: SuggestItineraryInputSchema,
        outputSchema: SuggestItineraryOutputSchema,
    },
    async (input) => {
        const { output } = await prompt({
            ...input,
            whatsappNumber: WHATSAPP_NUMBER,
        });
        return output!;
    },
);
