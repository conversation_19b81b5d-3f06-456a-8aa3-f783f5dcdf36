/**
 * Formats a number as a currency string (e.g., $1,234.56).
 *
 * @param amount - The number to format.
 * @param currency - The currency code (e.g., "USD", "EUR"). Defaults to "USD".
 * @param locale - The locale to use for formatting. Defaults to "en-US".
 * @returns The formatted currency string.
 */
export function formatCurrency(
    amount: number,
    currency = "USD",
    locale = "en-US",
): string {
    return new Intl.NumberFormat(locale, {
        style: "currency",
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    }).format(amount);
}
