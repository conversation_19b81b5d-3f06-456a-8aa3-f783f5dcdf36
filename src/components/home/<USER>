import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { cn } from "@/lib/utils";

// Constants for class names to improve maintainability
const HEADING_COMMON = cn(
    "font-bold tracking-tight text-gray-900 dark:text-gray-100",
);
const H1_CLASS = cn(
    "text-4xl sm:text-3xl mt-10 mb-6 border-b border-gray-200 dark:border-gray-700 pb-2 " +
        HEADING_COMMON,
);
const H2_CLASS = cn(
    "text-2xl sm:text-xl mt-8 mb-4 border-b border-gray-100 dark:border-gray-700 pb-1 " +
        HEADING_COMMON,
);
const H3_CLASS = cn("text-xl sm:text-lg mt-6 mb-2" + HEADING_COMMON);
const P_CLASS = cn("mb-4 text-gray-800 dark:text-gray-300 leading-relaxed");
const STRONG_CLASS = "font-semibold text-gray-900 dark:text-gray-100";
const EMPHASIS_CLASS = cn("italic text-gray-800 dark:text-gray-200");
const UL_CLASS = cn(
    "list-disc list-inside mb-4 space-y-1 pl-5 text-gray-800 dark:text-gray-300",
);
const OL_CLASS =
    "list-decimal list-inside mb-4 space-y-1 pl-5 text-gray-800 dark:text-gray-300";
const LI_CLASS = cn("marker:text-blue-500 dark:marker:text-blue-400");
const BLOCKQUOTE_CLASS = cn(
    "border-l-4 border-blue-300 dark:border-blue-500",
    "pl-4 py-2 mb-4 italic text-blue-800 dark:text-blue-200 bg-blue-50 dark:bg-blue-900/30 rounded-r",
);
const HR_CLASS = cn("my-8 border-t-2 border-gray-200 dark:border-gray-700");
const TABLE_CLASS = cn(
    "min-w-full border-collapse border border-gray-300 dark:border-gray-600 text-sm",
);
const TH_CLASS = cn(
    "border border-gray-300 dark:border-gray-600",
    "bg-gray-100 dark:bg-gray-700 px-4 py-2 text-left font-semibold",
);
const TD_CLASS = cn("border border-gray-300 dark:border-gray-600 px-4 py-2");

// Props interface
interface MarkdownDisplayProps {
    content: string;
    className?: string;
}

const MarkdownDisplay: React.FC<MarkdownDisplayProps> = ({
    content,
    className = "",
}) => (
    <div className={`prose prose-lg max-w-none ${className}`}>
        <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
                h1: ({ children, ...props }) => (
                    <h1 className={H1_CLASS} {...props}>
                        {children}
                    </h1>
                ),
                h2: ({ children, ...props }) => (
                    <h2 className={H2_CLASS} {...props}>
                        {children}
                    </h2>
                ),
                h3: ({ children, ...props }) => (
                    <h3 className={H3_CLASS} {...props}>
                        {children}
                    </h3>
                ),
                p: ({ children, ...props }) => (
                    <p className={P_CLASS} {...props}>
                        {children}
                    </p>
                ),
                strong: ({ children, ...props }) => (
                    <strong className={STRONG_CLASS} {...props}>
                        {children}
                    </strong>
                ),
                em: ({ children, ...props }) => (
                    <em className={EMPHASIS_CLASS} {...props}>
                        {children}
                    </em>
                ),
                ul: ({ children, ...props }) => (
                    <ul className={UL_CLASS} {...props}>
                        {children}
                    </ul>
                ),
                ol: ({ children, ...props }) => (
                    <ol className={OL_CLASS} {...props}>
                        {children}
                    </ol>
                ),
                li: ({ children, ...props }) => (
                    <li className={LI_CLASS} {...props}>
                        {children}
                    </li>
                ),
                a: ({ href, children, ...props }) => (
                    <a
                        href={href}
                        className={cn(
                            "text-blue-600 dark:text-blue-400",
                            "hover:text-blue-800 dark:hover:text-blue-300",
                            "underline transition-colors duration-200",
                        )}
                        target={href?.startsWith("http") ? "_blank" : undefined}
                        rel={
                            href?.startsWith("http")
                                ? "noopener noreferrer"
                                : undefined
                        }
                        {...props}
                    >
                        {children}
                    </a>
                ),
                blockquote: ({ children, ...props }) => (
                    <blockquote className={BLOCKQUOTE_CLASS} {...props}>
                        {children}
                    </blockquote>
                ),
                hr: (props) => <hr className={HR_CLASS} {...props} />,
                table: ({ children, ...props }) => (
                    <div className="overflow-x-auto mb-4">
                        <table className={TABLE_CLASS} {...props}>
                            {children}
                        </table>
                    </div>
                ),
                th: ({ children, ...props }) => (
                    <th className={TH_CLASS} {...props}>
                        {children}
                    </th>
                ),
                td: ({ children, ...props }) => (
                    <td className={TD_CLASS} {...props}>
                        {children}
                    </td>
                ),
            }}
        >
            {content}
        </ReactMarkdown>
    </div>
);

export default MarkdownDisplay;
