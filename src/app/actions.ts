"use server";

import { answerQuery, type AnswerQueryInput } from "@/ai/flows/answer-query";
import {
    suggestItinerary,
    type SuggestItineraryInput,
    type SuggestItineraryOutput,
} from "@/ai/flows/suggest-itinerary";
import {
    handleContactInquiry,
    type HandleContactInquiryInput,
} from "@/ai/flows/handle-contact-inquiry";

import { format } from "date-fns";
import { formatCurrency } from "@/lib/format";
import { searchSchema } from "@/constants/itinerary";
import { z } from "zod";
import {
    itineraryFastAPI,
    prepareFastAPIData,
    withFastAPIFallback,
} from "./fastapi-actions";

export async function handleQuery(input: AnswerQueryInput) {
    try {
        const result = await answerQuery(input);
        return { success: true, data: result };
    } catch (error) {
        console.error("Error in handleQuery:", error);
        return {
            success: false,
            error: "An unexpected error occurred. Please try again later.",
        };
    }
}

type ItineraryRequestResult =
    | { success: true; data: SuggestItineraryOutput }
    | { success: false; error: string; data?: undefined };

export async function itineraryGenKit(
    input: SuggestItineraryInput,
): Promise<ItineraryRequestResult> {
    try {
        const result = await suggestItinerary(input);
        return { success: true, data: result };
    } catch (error) {
        console.error("Error in itineraryGenKit:", error);
        return {
            success: false,
            error: "An unexpected error occurred while generating your itinerary. Please try again.",
        };
    }
}

export async function handleContactRequest(input: HandleContactInquiryInput) {
    try {
        const result = await handleContactInquiry(input);
        return { success: true, data: result };
    } catch (error) {
        console.error("Error in handleContactRequest:", error);
        return {
            success: false,
            error: "There was a problem sending your message. Please try again.",
        };
    }
}

export const prepareGenKitData = async (data: z.infer<typeof searchSchema>) => {
    let travelDates = "any time";
    if (data.date?.from) {
        if (data.date.to) {
            travelDates = `${format(data.date.from, "yyyy-MM-dd")} to ${format(
                data.date.to,
                "yyyy-MM-dd",
            )}`;
        } else {
            travelDates = format(data.date.from, "yyyy-MM-dd");
        }
    }
    return {
        interests: data.interests,
        travelDates: travelDates,
        budget: formatCurrency(data.budget),
    };
};

export async function generateItinerary(data: z.infer<typeof searchSchema>) {
    const fastAPIData = await prepareFastAPIData(data);
    const genKitData = await prepareGenKitData(data);

    return withFastAPIFallback(
        // FastAPI operation
        async () => {
            const itinerary = await itineraryFastAPI(fastAPIData);
            return { success: true, data: { answer: itinerary } };
        },
        // Fallback to Genkit operation
        async () => {
            const itinerary = await itineraryGenKit(genKitData);
            if (itinerary.success) {
                // Map itinerary to answer to match the expected type
                return {
                    success: true,
                    data: { answer: itinerary.data.itinerary },
                };
            } else {
                throw new Error(itinerary.error || "Genkit fallback failed");
            }
        },
    );
}
