ignore:
    - .git
    - venv
    - backend/.venv
    - .venv
    - env
    - .env
    - .tox
    - node_modules
    - vendor
    - dist
    - build
    - out
    - .pytest_cache
    - .mypy_cache
    - .ruff_cache
    - /opt/local/Library/

rule_settings:
    enable: [default]
    disable: []
    rule_types:
        - refactoring
        - suggestion
        - comment
    python_version: "3.13"

rules: []

metrics:
    quality_threshold: 25.0

github:
    labels: []
    ignore_labels:
        - sourcery-ignore
    request_review: author
    sourcery_branch: sourcery/{base_branch}

clone_detection:
    min_lines: 3
    min_duplicates: 2
    identical_clones_only: false

proxy:
    no_ssl_verify: false
