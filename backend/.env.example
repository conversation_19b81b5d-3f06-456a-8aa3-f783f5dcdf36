# =============================================================================
# AI Configuration
# =============================================================================
GEMINI_API_KEY="your_api_key_here"
AI_REQUEST_TIMEOUT=60
AI_MAX_RETRIES=2

# =============================================================================
# Environment
# =============================================================================
ENVIRONMENT=development
LOG_TO_FILE=false
PRODUCTION_FRONTEND_URL=

# =============================================================================
# Email Configuration
# =============================================================================
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="your_email_password"
MAIL_FROM="<EMAIL>"
MAIL_PORT=587
MAIL_SERVER="smtp.example.com"
MAIL_STARTTLS=true
MAIL_SSL_TLS=false

# =============================================================================
# Redis Configuration (Optional - uses in-memory fallback if disabled)
# =============================================================================
REDIS_ENABLED=true
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# =============================================================================
# Docker Configuration
# =============================================================================
# Docker Compose settings
BUILD_TARGET=development
BACKEND_PORT=8000
MOUNT_MODE=rw
REDIS_COMMANDER_PORT=8081

# Docker resource limits (production)
BACKEND_MEMORY_LIMIT=1G
BACKEND_CPU_LIMIT=1.0
REDIS_MEMORY_LIMIT=512M
REDIS_CPU_LIMIT=0.5

# =============================================================================
# Performance Configuration
# =============================================================================
MAX_CONCURRENT_AI_REQUESTS=10
ENABLE_RESPONSE_CACHING=true
CACHE_TTL_ITINERARY=86400
CACHE_TTL_QUERY=3600
CACHE_TTL_CONTACT=1800

# =============================================================================
# Rate Limiting
# =============================================================================
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600
