"""Load testing script for BaliBlissed backend API.

This script demonstrates how to perform load testing on the optimized backend
to verify performance improvements and identify bottlenecks.

Requirements:
    pip install locust

Usage:
    locust -f load_test_example.py --host=http://localhost:8000

    Then open http://localhost:8089 in your browser to configure and run tests.
"""

from secrets import choice, randbelow
from typing import Any

from locust import HttpUser, between, task


class BaliBlissedUser(HttpUser):
    """Simulated user for load testing BaliBlissed API."""

    # Wait 1-5 seconds between tasks
    wait_time = between(1, 5)

    # Sample data for testing
    destinations = ["Bali", "Ubud", "Seminyak", "Canggu", "Nusa Dua"]
    interests = [
        ["beaches", "surfing", "nightlife"],
        ["temples", "culture", "yoga"],
        ["food", "shopping", "spas"],
        ["adventure", "hiking", "waterfalls"],
        ["diving", "snorkeling", "marine life"],
    ]
    budgets = ["$500", "$1000", "$1500", "$2000", "$3000"]
    durations = [3, 5, 7, 10, 14]

    queries = [
        "What are the best beaches in Bali?",
        "Where can I find authentic Balinese food?",
        "What temples should I visit in Ubud?",
        "How much does a surf lesson cost?",
        "What's the best time to visit Bali?",
        "Are there any yoga retreats in Ubud?",
        "What are the top attractions in Seminyak?",
        "How do I get from the airport to Ubud?",
        "What should I pack for a trip to Bali?",
        "Are there any good hiking trails near Ubud?",
    ]

    contact_messages = [
        "I'm interested in booking a 7-day trip to Bali. Can you help?",
        "What packages do you offer for honeymoon trips?",
        "I need help planning a family vacation to Bali.",
        "Can you customize an itinerary for adventure activities?",
        "I'd like to know more about your yoga retreat packages.",
    ]

    def on_start(self) -> None:
        """Simulate user starts."""
        self.client.headers = {"Content-Type": "application/json"}

    @task(3)
    def suggest_itinerary(self) -> None:
        """Test itinerary generation endpoint (30% of requests)."""
        payload = {
            "destination": choice(self.destinations),
            "duration": choice(self.durations),
            "interests": choice(self.interests),
            "budget": choice(self.budgets),
        }

        with self.client.post(
            "/api/suggest-itinerary",
            json=payload,
            catch_response=True,
            name="/api/suggest-itinerary",
        ) as response:
            if response.status_code in [200, 429]:
                response.success()
            else:
                response.failure(f"Got status code {response.status_code}")

    @task(6)
    def answer_query(self) -> None:
        """Test chatbot endpoint (60% of requests)."""
        payload = {
            "query": choice(self.queries),
            "history": [],
        }

        with self.client.post(
            "/api/answer-query",
            json=payload,
            catch_response=True,
            name="/api/answer-query",
        ) as response:
            if response.status_code in [200, 429]:
                response.success()
            else:
                response.failure(f"Got status code {response.status_code}")

    @task(1)
    def handle_contact_inquiry(self) -> None:
        """Test contact form endpoint (10% of requests)."""
        payload = {
            "name": f"Test User {randbelow(1000) + 1}",
            "email": f"test{randbelow(1000) + 1}@example.com",
            "message": choice(self.contact_messages),
        }

        with self.client.post(
            "/api/handle-contact-inquiry",
            json=payload,
            catch_response=True,
            name="/api/handle-contact-inquiry",
        ) as response:
            if response.status_code in [200, 429]:
                response.success()
            else:
                response.failure(f"Got status code {response.status_code}")

    @task(2)
    def check_health(self) -> None:
        """Test health check endpoint (20% of requests)."""
        with self.client.get(
            "/health",
            catch_response=True,
            name="/health",
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Got status code {response.status_code}")

    @task(1)
    def get_metrics(self) -> None:
        """Test metrics endpoint (10% of requests)."""
        with self.client.get(
            "/metrics",
            catch_response=True,
            name="/metrics",
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Got status code {response.status_code}")


class CacheTestUser(HttpUser):
    """User that tests cache performance by making identical requests."""

    wait_time = between(0.5, 2)

    # Fixed payload for cache testing
    itinerary_payload = {
        "destination": "Bali",
        "duration": 7,
        "interests": ["beaches", "temples", "food"],
        "budget": "$1000",
    }

    query_payload = {
        "query": "What are the best beaches in Bali?",
        "history": [],
    }

    @task(5)
    def cached_itinerary(self) -> None:
        """Test cached itinerary requests."""
        with self.client.post(
            "/api/suggest-itinerary",
            json=self.itinerary_payload,
            catch_response=True,
            name="/api/suggest-itinerary (cached)",
        ) as response:
            if response.status_code == 200:
                # Check if response was fast (likely cached)
                if response.elapsed.total_seconds() < 1.0:
                    response.success()
                else:
                    response.success()  # Still success, just not cached
            else:
                response.failure(f"Got status code {response.status_code}")

    @task(5)
    def cached_query(self) -> None:
        """Test cached query requests."""
        with self.client.post(
            "/api/answer-query",
            json=self.query_payload,
            catch_response=True,
            name="/api/answer-query (cached)",
        ) as response:
            if response.status_code == 200:
                # Check if response was fast (likely cached)
                if response.elapsed.total_seconds() < 1.0:
                    response.success()
                else:
                    response.success()  # Still success, just not cached
            else:
                response.failure(f"Got status code {response.status_code}")


class RateLimitTestUser(HttpUser):
    """User that tests rate limiting by making rapid requests."""

    wait_time = between(0.1, 0.5)

    @task
    def rapid_itinerary_requests(self) -> None:
        """Make rapid itinerary requests to test rate limiting."""
        payload = {
            "destination": "Bali",
            "duration": 7,
            "interests": ["beaches"],
            "budget": "$1000",
        }

        with self.client.post(
            "/api/suggest-itinerary",
            json=payload,
            catch_response=True,
            name="/api/suggest-itinerary (rate limit test)",
        ) as response:
            if response.status_code in [200, 429]:
                response.success()
            else:
                response.failure(f"Got status code {response.status_code}")


# Example usage:
#
# 1. Basic load test (mixed traffic):
#    locust -f load_test_example.py --host=http://localhost:8000 --users=50 --spawn-rate=5
#
# 2. Cache performance test:
#    locust -f load_test_example.py --host=http://localhost:8000 --users=20 --spawn-rate=2 CacheTestUser
#
# 3. Rate limit test:
#    locust -f load_test_example.py --host=http://localhost:8000 --users=10 --spawn-rate=10 RateLimitTestUser
#
# 4. Web UI (recommended):
#    locust -f load_test_example.py --host=http://localhost:8000
#    Then open http://localhost:8089 in your browser


if __name__ == "__main__":
    print("Load testing script for BaliBlissed backend API")
    print("\nUsage:")
    print("  locust -f load_test_example.py --host=http://localhost:8000")
    print("\nThen open http://localhost:8089 in your browser")
    print("\nTest scenarios:")
    print("  - BaliBlissedUser: Mixed traffic simulation")
    print("  - CacheTestUser: Cache performance testing")
    print("  - RateLimitTestUser: Rate limiting verification")
