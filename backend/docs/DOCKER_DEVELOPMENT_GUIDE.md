# Docker Development Guide

A comprehensive guide for updating Docker configuration as your BaliBlissed project grows.

## 📋 Overview

This guide covers which Docker files to update when adding new features, packages, databases, and services to your project.

## 🐍 Adding Python Packages

### Files to Update

#### 1. `backend/pyproject.toml` (Primary - Recommended)

```toml
[project]
dependencies = [
    "fastapi>=0.116.2",
    "uvicorn[standard]>=0.36.0",
    "redis>=5.0.0",
    # Add new packages here
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "psycopg2-binary>=2.9.0"
]
```

#### 2. `backend/Dockerfile` (Backup - if pyproject.toml doesn't work)

```dockerfile
# Global pip installations for missing dependencies
RUN pip install uvicorn[standard]>=0.36.0 fastapi>=0.116.2 pydantic[email]>=2.9.0 \
    # Add new packages here if needed
    sqlalchemy>=2.0.0 alembic>=1.13.0 psycopg2-binary>=2.9.0
```

### Workflow

```bash
# 1. Add package to pyproject.toml
# 2. Rebuild container
npm run docker:stop
npm run docker:start  # Will rebuild with --build flag
```

## 🗄️ Adding Database (PostgreSQL Example)

### File to Update 1: `backend/docker-compose.yml`

Add database service:

```yaml
services:
    # Add this new service
    postgres:
        image: postgres:15-alpine
        container_name: baliblissed-postgres
        restart: unless-stopped
        ports:
            - "${POSTGRES_PORT:-5432}:5432"
        environment:
            - POSTGRES_DB=${POSTGRES_DB:-baliblissed}
            - POSTGRES_USER=${POSTGRES_USER:-postgres}
            - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}
        volumes:
            - postgres_data:/var/lib/postgresql/data
            - ./database/init:/docker-entrypoint-initdb.d:ro
        healthcheck:
            test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
            interval: 30s
            timeout: 10s
            retries: 3
        networks:
            - baliblissed-network
        profiles:
            - development
            - production

    # Update backend service dependencies
    backend:
        # ... existing config ...
        depends_on:
            redis:
                condition: service_healthy
            postgres:  # Add this
                condition: service_healthy

# Add to volumes section
volumes:
    postgres_data:
        driver: local
        driver_opts:
            type: none
            o: bind
            device: ../data/postgres
```

## 📁 Adding More Python Files

**✅ No Docker files need updating!**

Your current setup automatically handles new Python files:

- Volume mount: `.:/app` includes all files
- Auto-reload: Uvicorn watches all Python files

Just create files in:

- `backend/app/` - Main application code
- `backend/app/routers/` - API routes
- `backend/app/models/` - Database models
- `backend/app/services/` - Business logic

## 🔧 Adding New Services

### File to Update 2: `backend/docker-compose.yml`

Example - Adding RabbitMQ:

```yaml
services:
    rabbitmq:
        image: rabbitmq:3-management-alpine
        container_name: baliblissed-rabbitmq
        restart: unless-stopped
        ports:
            - "${RABBITMQ_PORT:-5672}:5672"
            - "${RABBITMQ_MANAGEMENT_PORT:-15672}:15672"
        environment:
            - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER:-admin}
            - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD:-password}
        volumes:
            - rabbitmq_data:/var/lib/rabbitmq
        networks:
            - baliblissed-network
        profiles:
            - development

volumes:
    rabbitmq_data:
        driver: local
        driver_opts:
            type: none
            o: bind
            device: ../data/rabbitmq
```

## 🌍 Environment Variables

### Files to Update 3

1. **`backend/docker-compose.yml`** - Add to backend service environment
2. **`.env`** - Add default values
3. **`.env.example`** - Document new variables

Example:

```yaml
# In docker-compose.yml backend service
environment:
    - DATABASE_URL=${DATABASE_URL:-********************************************/baliblissed}
    - RABBITMQ_URL=${RABBITMQ_URL:-amqp://admin:password@rabbitmq:5672/}
```

## 📊 Development vs Production

Use profiles to separate environments:

```yaml
services:
    # Development-only services
    redis-commander:
        # ... config ...
        profiles:
            - development
            - debug
    
    # Production-only services  
    nginx:
        # ... config ...
        profiles:
            - production
```

## 🚀 Quick Reference

| **Adding...**    | **Update Files**                      | **Rebuild Required?** |
| ---------------- | ------------------------------------- | --------------------- |
| Python packages  | `pyproject.toml` or `Dockerfile`      | ✅ Yes                 |
| Python files     | None                                  | ❌ No (auto-reload)    |
| Database         | `docker-compose.yml`                  | ✅ Yes                 |
| New services     | `docker-compose.yml`                  | ✅ Yes                 |
| Environment vars | `docker-compose.yml`, `.env`          | ❌ No (restart only)   |
| Config files     | Volume mounts in `docker-compose.yml` | ❌ No                  |

## 🔄 Development Workflow

```bash
# For package/service changes
npm run docker:stop
npm run docker:start  # Rebuilds automatically

# For code changes only
# No action needed - auto-reload handles it

# Check what's running
npm run docker:status

# View logs
npm run docker:logs [service-name]
```

## 📝 Common Database Examples

### MySQL

```yaml
mysql:
    image: mysql:8.0
    container_name: baliblissed-mysql
    environment:
        - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-rootpassword}
        - MYSQL_DATABASE=${MYSQL_DATABASE:-baliblissed}
        - MYSQL_USER=${MYSQL_USER:-user}
        - MYSQL_PASSWORD=${MYSQL_PASSWORD:-password}
    volumes:
        - mysql_data:/var/lib/mysql
    healthcheck:
        test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
        interval: 30s
        timeout: 10s
        retries: 3
```

### MongoDB

```yaml
mongodb:
    image: mongo:7
    container_name: baliblissed-mongodb
    environment:
        - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-admin}
        - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD:-password}
        - MONGO_INITDB_DATABASE=${MONGO_DATABASE:-baliblissed}
    volumes:
        - mongodb_data:/data/db
    healthcheck:
        test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
        interval: 30s
        timeout: 10s
        retries: 3
```

## 🎯 Best Practices

1. **Always use profiles** for environment-specific services
2. **Use health checks** for all services
3. **Mount volumes** for persistent data
4. **Use environment variables** for configuration
5. **Document new variables** in `.env.example`
6. **Test locally** before deploying

---

Your Docker setup is designed to scale easily - just update the relevant files and restart! 🚀
