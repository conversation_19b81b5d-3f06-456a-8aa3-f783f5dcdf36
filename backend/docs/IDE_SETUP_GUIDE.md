# 🛠️ IDE Setup Guide for BaliBlissed Backend

This guide explains how to set up your IDE for optimal development experience with the BaliBlissed backend.

## 🎯 **Problem Solved**

**Issue**: IDE shows import errors for FastAPI, Pydantic, and other dependencies even though Docker containers work perfectly.

**Root Cause**: IDE doesn't have access to the Docker virtual environment, so it can't resolve imports for intellisense, error checking, and auto-completion.

**Solution**: Local virtual environment that mirrors the Docker setup + proper IDE configuration.

---

## ✅ **Current Setup (Already Configured)**

### **1. Local Virtual Environment**

- **Location**: `backend/.venv/` (matches Docker path)
- **Python Version**: 3.13.7
- **Package Manager**: `uv` (same as Docker)
- **Dependencies**: All 94 packages installed (matches Docker exactly)

### **2. VS Code Configuration**

- **Settings**: `.vscode/settings.json` configured
- **Python Path**: Points to `./backend/.venv/bin/python`
- **Pyright Config**: `backend/pyrightconfig.json` for advanced type checking

### **3. Import Resolution**

✅ All major dependencies working:

- FastAPI, Pydantic, Redis, Google Generative AI
- All app modules: settings, services, middleware, schemas

---

## 🚀 **Development Workflow**

### **Option 1: Hybrid Development (Recommended)**

```bash
# Start Docker services (Redis, backend API)
npm run docker:start

# Start frontend natively (better performance)
npm run dev:next

# Your IDE now has full intellisense for backend code!
```

### **Option 2: Full Docker Development**

```bash
# Start everything in Docker
npm run dev

# IDE still has intellisense thanks to local .venv
```

### **Option 3: Local Backend Development**

```bash
# Activate local environment
cd backend
source .venv/bin/activate

# Start Redis in Docker
npm run docker:start

# Run backend locally
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

---

## 🔧 **IDE Features Now Available**

### **✅ Import Resolution**

- No more red squiggly lines on imports
- Auto-completion for all dependencies
- Jump to definition works

### **✅ Type Checking**

- Full Pydantic model validation
- FastAPI route parameter checking
- Type hints and error detection

### **✅ Code Intelligence**

- Auto-imports
- Refactoring support
- Symbol search across codebase

### **✅ Debugging**

- Set breakpoints in backend code
- Step through code execution
- Variable inspection

---

## 📁 **File Structure**

```plain text
backend/
├── .venv/                    # Local virtual environment (mirrors Docker)
├── .venv-local/             # Backup (can be deleted)
├── pyrightconfig.json       # Python type checker config
├── app/                     # Your application code
│   ├── main.py             # ✅ No more import errors!
│   ├── config/
│   ├── services/
│   └── ...
└── docs/
    └── IDE_SETUP_GUIDE.md   # This file

.vscode/
└── settings.json            # VS Code Python configuration
```

---

## 🔄 **Keeping Environments in Sync**

### **When Adding New Dependencies**

1. **Add to pyproject.toml**:

   ```toml
   [project]
   dependencies = [
       "fastapi>=0.116.2",
       "new-package>=1.0.0",  # Add here
   ]
   ```

2. **Update both environments**:

   ```bash
   # Update local environment
   cd backend
   source .venv/bin/activate
   uv sync

   # Rebuild Docker
   npm run docker:stop
   npm run docker:start
   ```

### **Automatic Sync**

- Both environments use the same `pyproject.toml`
- Both use `uv` package manager
- Versions stay consistent automatically

---

## 🐛 **Troubleshooting**

### **Import Errors Still Showing?**

1. **Restart VS Code**: `Cmd+Shift+P` → "Developer: Reload Window"

2. **Select Correct Python Interpreter**:
   - `Cmd+Shift+P` → "Python: Select Interpreter"
   - Choose: `./backend/.venv/bin/python`

3. **Clear Python Cache**:

   ```bash
   cd backend
   find . -name "__pycache__" -type d -exec rm -rf {} +
   ```

### **Environment Issues?**

1. **Recreate Local Environment**:

   ```bash
   cd backend
   rm -rf .venv
   python3 -m venv .venv
   source .venv/bin/activate
   pip install uv
   uv sync
   ```

2. **Check Environment Variables**:

   ```bash
   cd backend
   source .venv/bin/activate
   python -c "import sys; print(sys.prefix)"
   # Should show: /path/to/backend/.venv
   ```

---

## 🎯 **Benefits of This Setup**

### **✅ Best of Both Worlds**

- **Docker**: Consistent runtime environment, easy deployment
- **Local IDE**: Full development features, fast intellisense

### **✅ No Compromises**

- Keep using Docker for services (Redis, databases)
- Get full IDE support for Python development
- Maintain environment consistency

### **✅ Team Friendly**

- Same setup works for all developers
- No complex IDE-specific configurations
- Easy onboarding for new team members

---

## 🚀 **Next Steps**

1. **Restart VS Code** to apply all configurations
2. **Open any Python file** in `backend/app/`
3. **Verify imports work** - no red squiggly lines!
4. **Start coding** with full IDE support! 🎉

---

## 📚 **Additional Resources**

- [VS Code Python Extension](https://marketplace.visualstudio.com/items?itemName=ms-python.python)
- [Pyright Type Checker](https://github.com/microsoft/pyright)
- [UV Package Manager](https://github.com/astral-sh/uv)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)

---

**🎉 Happy Coding!** Your IDE now has full support for the BaliBlissed backend development!
