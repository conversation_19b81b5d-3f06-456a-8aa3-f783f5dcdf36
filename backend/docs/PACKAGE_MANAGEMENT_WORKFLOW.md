# Package Management Workflow

## 🎯 Overview

This document explains the complete workflow for managing Python packages in the BaliBlissed development environment, which uses both local and Docker environments.

## 📍 Package Installation Locations

### Local Environment

- **Location**: `backend/.venv/lib/python3.13/site-packages/`
- **Purpose**: IDE support, intellisense, type checking
- **Immediate availability**: ✅ Yes

### Docker Environment  

- **Location**: `/app/.venv/lib/python3.13/site-packages/` (inside container)
- **Purpose**: Runtime execution, production consistency
- **Immediate availability**: ❌ No (requires rebuild)

## 🔄 Auto-Reload Behavior

| Change Type      | Local Environment | Docker Environment |
| ---------------- | ----------------- | ------------------ |
| **Code Changes** | ✅ Immediate       | ✅ Hot reload       |
| **New Packages** | ✅ Immediate       | ❌ Requires rebuild |

**Why no auto-reload for packages?**

- **Stability**: Prevents runtime conflicts
- **Consistency**: Ensures reproducible builds
- **Performance**: Avoids package resolution overhead

## 📋 Complete Workflow

### Method 1: Standard Workflow (Recommended)

```bash
# 1. Add package to local environment
cd backend
source .venv/bin/activate  # Use the local environment
uv add somepackage

# 2. Sync to Docker (rebuild required)
npm run docker:stop
npm run docker:start  # Automatically rebuilds with new dependencies

# 3. Verify package is available
docker exec baliblissed-backend python -c "import somepackage; print('✅ Package available!')"
```

### Method 2: Quick Test (Temporary)

```bash
# For quick testing only (not persistent)
docker exec baliblissed-backend uv add somepackage

# Note: This will be lost when container restarts!
```

## 🔧 Files Updated Automatically

When you run `uv add somepackage`:

1. **`pyproject.toml`**: Adds dependency with version constraint
2. **`uv.lock`**: Updates with exact versions and dependency tree
3. **Local `.venv`**: Installs package immediately

## 🐳 Docker Synchronization Process

1. **Docker Build**: Reads `pyproject.toml` and `uv.lock`
2. **Package Installation**: Runs `uv sync --frozen` to install exact versions
3. **Environment Setup**: Creates identical environment to local

## 🚀 Development Workflows

### Option 1: Hybrid Development (Recommended)

```bash
npm run dev:next      # Frontend natively
npm run dev:docker    # Backend + Redis in Docker
# IDE has full intellisense from local .venv!
```

### Option 2: Full Docker

```bash
npm run dev           # Everything in Docker
# IDE still has intellisense thanks to local .venv
```

## 🔍 Troubleshooting

### Package Not Available in Docker

```bash
# Check if package is in pyproject.toml
cat pyproject.toml | grep somepackage

# Rebuild Docker environment
npm run docker:stop
npm run docker:start

# Verify installation
docker exec baliblissed-backend pip list | grep somepackage
```

### IDE Not Recognizing Package

```bash
# Ensure local environment is active
source .venv/bin/activate
uv add somepackage

# Restart VS Code
# Check Python interpreter points to .venv/bin/python
```

## 📝 Best Practices

1. **Always use `uv add/remove`** instead of editing `pyproject.toml` manually
2. **Test locally first** before rebuilding Docker
3. **Rebuild Docker** after adding packages for production consistency
4. **Use semantic versioning** constraints in `pyproject.toml`
5. **Commit both** `pyproject.toml` and `uv.lock` to version control

## 🚨 Common Issues & Solutions

### Docker Architecture Mismatch Error
**Error**: `exec /app/.venv/bin/uv: exec format error`

**Cause**: Local `.venv` directory contains macOS binaries, but Docker needs Linux binaries.

**Solution**: Updated `docker-compose.yml` to exclude `.venv` from volume mounts:
```yaml
volumes:
    # Mount specific files/directories (excluding .venv)
    - ./app:/app/app
    - ./scripts:/app/scripts
    - ./pyproject.toml:/app/pyproject.toml
    - ./uv.lock:/app/uv.lock
    # ... other specific mounts
```

### npm run dev:docker "Hanging"
**Behavior**: Command appears to hang after starting Docker services.

**Explanation**: This is **normal behavior** - the command follows Docker logs indefinitely (`-f` flag).

**Usage**:
- Use `Ctrl+C` to stop following logs
- Docker services continue running in background
- Use `npm run docker:status` to check if services are running

## 🔗 Related Documentation

- [IDE Setup Guide](./IDE_SETUP_GUIDE.md)
- [Docker Development Guide](./DOCKER_DEVELOPMENT.md)
- [Environment Configuration](./ENVIRONMENT_SETUP.md)
