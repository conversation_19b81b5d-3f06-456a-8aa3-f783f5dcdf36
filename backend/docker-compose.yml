# docker-compose.yml
# Docker Compose configuration for BaliBlissed backend with Redis
# Supports both development and production environments

version: "3.8"

services:
    # Redis Cache Service
    redis:
        image: redis:7-alpine
        container_name: baliblissed-redis
        restart: unless-stopped
        ports:
            - "${REDIS_PORT:-6379}:6379"
        volumes:
            # Persistent data storage
            - redis_data:/data
            # Custom Redis configuration
            - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
        command: redis-server /usr/local/etc/redis/redis.conf
        environment:
            # Redis configuration via environment variables
            - REDIS_PASSWORD=${REDIS_PASSWORD:-}
        healthcheck:
            test: ["CMD", "redis-cli", "ping"]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 30s
        networks:
            - baliblissed-network
        # Resource limits for production
        deploy:
            resources:
                limits:
                    memory: 512M
                    cpus: "0.5"
                reservations:
                    memory: 256M
                    cpus: "0.25"

    # FastAPI Backend Service
    backend:
        build:
            context: .
            dockerfile: Dockerfile
            target: ${BUILD_TARGET:-development}
        container_name: baliblissed-backend
        restart: unless-stopped
        ports:
            - "${BACKEND_PORT:-8000}:8000"
        environment:
            # Redis connection
            - REDIS_ENABLED=true
            - REDIS_HOST=redis
            - REDIS_PORT=6379
            - REDIS_DB=${REDIS_DB:-0}
            - REDIS_PASSWORD=${REDIS_PASSWORD:-}

            # Application environment
            - ENVIRONMENT=${ENVIRONMENT:-development}
            - LOG_TO_FILE=${LOG_TO_FILE:-false}
            - PRODUCTION_FRONTEND_URL=${PRODUCTION_FRONTEND_URL:-}

            # AI Configuration
            - GEMINI_API_KEY=${GEMINI_API_KEY}
            - AI_REQUEST_TIMEOUT=${AI_REQUEST_TIMEOUT:-60}
            - AI_MAX_RETRIES=${AI_MAX_RETRIES:-2}

            # Email Configuration
            - MAIL_USERNAME=${MAIL_USERNAME}
            - MAIL_PASSWORD=${MAIL_PASSWORD}
            - MAIL_FROM=${MAIL_FROM}
            - MAIL_PORT=${MAIL_PORT:-587}
            - MAIL_SERVER=${MAIL_SERVER}
            - MAIL_STARTTLS=${MAIL_STARTTLS:-true}
            - MAIL_SSL_TLS=${MAIL_SSL_TLS:-false}

            # Performance Configuration
            - MAX_CONCURRENT_AI_REQUESTS=${MAX_CONCURRENT_AI_REQUESTS:-10}
            - ENABLE_RESPONSE_CACHING=${ENABLE_RESPONSE_CACHING:-true}
            - CACHE_TTL_ITINERARY=${CACHE_TTL_ITINERARY:-86400}
            - CACHE_TTL_QUERY=${CACHE_TTL_QUERY:-3600}
            - CACHE_TTL_CONTACT=${CACHE_TTL_CONTACT:-1800}

            # Rate Limiting
            - RATE_LIMIT_REQUESTS=${RATE_LIMIT_REQUESTS:-100}
            - RATE_LIMIT_WINDOW=${RATE_LIMIT_WINDOW:-3600}
        volumes:
            # Development: Mount source code for hot reload (excluding .venv)
            - ./app:/app/app
            - ./scripts:/app/scripts
            - ./docs:/app/docs
            - ./pyproject.toml:/app/pyproject.toml
            - ./uv.lock:/app/uv.lock
            - ./.env:/app/.env
            - ./main.py:/app/main.py
            - ./requirements.txt:/app/requirements.txt
            # Logs directory
            - backend_logs:/app/logs
        depends_on:
            redis:
                condition: service_healthy
        healthcheck:
            test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 60s
        networks:
            - baliblissed-network
        # Resource limits for production
        deploy:
            resources:
                limits:
                    memory: 1G
                    cpus: "1.0"
                reservations:
                    memory: 512M
                    cpus: "0.5"

    # Redis Commander (Optional - for development)
    redis-commander:
        image: rediscommander/redis-commander:latest
        container_name: baliblissed-redis-commander
        restart: unless-stopped
        ports:
            - "${REDIS_COMMANDER_PORT:-8081}:8081"
        environment:
            - REDIS_HOSTS=local:redis:6379
            - REDIS_PASSWORD=${REDIS_PASSWORD:-}
        depends_on:
            redis:
                condition: service_healthy
        networks:
            - baliblissed-network
        profiles:
            - development
            - debug

# Named volumes for data persistence
volumes:
    redis_data:
        driver: local
        driver_opts:
            type: none
            o: bind
            device: ../data/redis
    backend_logs:
        driver: local
        driver_opts:
            type: none
            o: bind
            device: ../logs

# Custom network for service communication
networks:
    baliblissed-network:
        driver: bridge
        ipam:
            config:
                - subnet: **********/16
