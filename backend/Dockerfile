# backend/Dockerfile
# Multi-stage Dockerfile for BaliBlissed FastAPI backend with Redis support
# Optimized for both development and production environments

# =============================================================================
# Base Stage - Common dependencies and setup
# =============================================================================
FROM python:3.13-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app \
    UV_NO_CACHE=1 \
    VIRTUAL_ENV=/app/.venv \
    PATH="/app/.venv/bin:$PATH"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Essential build tools
    build-essential \
    # Network tools for health checks
    curl \
    # Redis client for health checks
    redis-tools \
    # Clean up
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create app user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Install uv for faster dependency management with retry logic
RUN pip install --retries 3 --timeout 30 uv || pip install --no-cache-dir uv

# =============================================================================
# Dependencies Stage - Install Python dependencies
# =============================================================================
FROM base as dependencies

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Install dependencies with uv into virtual environment
RUN uv sync --frozen --no-dev

# =============================================================================
# Development Stage - For development with hot reload
# =============================================================================
FROM dependencies as development

# Install all dependencies including dev dependencies
RUN uv sync --frozen

# Copy application code
COPY . .

# Create logs directory
RUN mkdir -p /app/logs && chown -R appuser:appuser /app/logs

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Development command with hot reload
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# =============================================================================
# Production Stage - Optimized for production deployment
# =============================================================================
FROM dependencies as production

# Copy application code
COPY . .

# Create logs directory, set permissions
RUN mkdir -p /app/logs && \
    chown -R appuser:appuser /app && \
    chmod -R 755 /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Production command with optimizations
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker"]

# =============================================================================
# Testing Stage - For running tests in CI/CD
# =============================================================================
FROM development as testing

# Install test dependencies
RUN uv add --dev pytest pytest-asyncio pytest-cov httpx

# Copy test files
COPY tests/ ./tests/

# Run tests
CMD ["python", "-m", "pytest", "tests/", "-v", "--cov=app", "--cov-report=term-missing"]
