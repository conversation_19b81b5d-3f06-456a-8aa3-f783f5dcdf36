#!/bin/bash
# scripts/setup.sh
# Initial setup script for BaliBlissed Docker environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker and try again."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    log_success "Prerequisites check passed!"
}

# Setup environment files
setup_environment() {
    log_info "Setting up environment files..."
    
    # Development environment
    if [ ! -f ".env" ]; then
        if [ -f ".env.development.example" ]; then
            cp .env.development.example .env
            log_success "Created .env from .env.development.example"
            log_warning "Please update .env with your actual configuration values!"
        else
            log_error ".env.development.example not found!"
            exit 1
        fi
    else
        log_info ".env already exists, skipping..."
    fi
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        if [ -f "backend/.env.example" ]; then
            cp backend/.env.example backend/.env
            log_success "Created backend/.env from backend/.env.example"
        else
            log_warning "backend/.env.example not found, skipping..."
        fi
    else
        log_info "backend/.env already exists, skipping..."
    fi
}

# Create required directories
create_directories() {
    log_info "Creating required directories..."
    
    mkdir -p data/redis
    mkdir -p logs
    mkdir -p backups
    
    # Set proper permissions
    chmod 755 data/redis logs backups
    
    log_success "Directories created successfully!"
}

# Make scripts executable
setup_scripts() {
    log_info "Setting up helper scripts..."
    
    chmod +x scripts/docker-dev.sh
    chmod +x scripts/docker-prod.sh
    chmod +x scripts/setup.sh
    
    log_success "Scripts are now executable!"
}

# Display next steps
show_next_steps() {
    echo
    log_success "Setup completed successfully!"
    echo
    log_info "Next steps:"
    echo "1. Update your .env file with actual configuration values:"
    echo "   - GEMINI_API_KEY: Your Google Gemini API key"
    echo "   - MAIL_USERNAME: Your email address"
    echo "   - MAIL_PASSWORD: Your email app password"
    echo
    log_info "2. Start the development environment:"
    echo "   ./scripts/docker-dev.sh start"
    echo
    log_info "3. Access your application:"
    echo "   - Backend API: http://localhost:8000"
    echo "   - API Documentation: http://localhost:8000/docs"
    echo "   - Redis Commander: http://localhost:8081"
    echo
    log_info "4. Useful commands:"
    echo "   - View logs: ./scripts/docker-dev.sh logs"
    echo "   - Check status: ./scripts/docker-dev.sh status"
    echo "   - Stop services: ./scripts/docker-dev.sh stop"
    echo "   - Get help: ./scripts/docker-dev.sh help"
    echo
    log_info "For production deployment:"
    echo "   1. Copy .env.production.example to .env.production"
    echo "   2. Configure production values (especially passwords!)"
    echo "   3. Run: ./scripts/docker-prod.sh deploy"
    echo
}

# Main setup function
main() {
    echo "🚀 BaliBlissed Docker Environment Setup"
    echo "======================================"
    echo
    
    check_prerequisites
    setup_environment
    create_directories
    setup_scripts
    show_next_steps
}

# Run main function
main "$@"
