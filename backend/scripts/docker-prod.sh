#!/bin/bash
# scripts/docker-prod.sh
# Production Docker management script for BaliBlissed

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.yml"
ENV_FILE="../.env.production"
PROJECT_NAME="baliblissed-prod"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Check if production environment file exists
check_env() {
    if [ ! -f "$ENV_FILE" ]; then
        log_error "Production environment file $ENV_FILE not found."
        if [ -f "../.env.production.example" ]; then
            log_info "Please copy .env.production.example to $ENV_FILE and configure it:"
            log_info "cp ../.env.production.example $ENV_FILE"
            log_warning "Make sure to set secure passwords and production values!"
        fi
        exit 1
    fi
    
    # Check for required production variables
    if ! grep -q "REDIS_PASSWORD=" "$ENV_FILE" || grep -q "REDIS_PASSWORD=$" "$ENV_FILE"; then
        log_warning "REDIS_PASSWORD is not set in $ENV_FILE. This is required for production!"
    fi
}

# Create required directories with proper permissions
create_directories() {
    log_info "Creating required directories for production..."
    mkdir -p ../data/redis ../logs
    chmod 755 ../data/redis ../logs
    log_success "Directories created successfully"
}

# Deploy production environment
deploy() {
    log_info "Deploying BaliBlissed production environment..."
    check_docker
    check_env
    create_directories
    
    # Pull latest images
    log_info "Pulling latest images..."
    docker-compose --env-file "$ENV_FILE" pull
    
    # Build and start services
    log_info "Building and starting services..."
    docker-compose --env-file "$ENV_FILE" up --build -d
    
    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    sleep 30
    
    # Check health
    if health_check; then
        log_success "Production environment deployed successfully!"
        log_info "Backend API: http://localhost:8000"
        log_info "Health Check: http://localhost:8000/health"
    else
        log_error "Health check failed. Please check logs."
        exit 1
    fi
}

# Stop production environment
stop() {
    log_info "Stopping BaliBlissed production environment..."
    docker-compose --env-file "$ENV_FILE" down
    log_success "Production environment stopped successfully!"
}

# Update production environment
update() {
    log_info "Updating BaliBlissed production environment..."
    
    # Pull latest images
    docker-compose --env-file "$ENV_FILE" pull
    
    # Recreate services with zero downtime
    docker-compose --env-file "$ENV_FILE" up -d --no-deps backend
    
    # Wait for health check
    sleep 30
    if health_check; then
        log_success "Production environment updated successfully!"
    else
        log_error "Update failed. Rolling back..."
        rollback
    fi
}

# Rollback to previous version
rollback() {
    log_warning "Rolling back to previous version..."
    docker-compose --env-file "$ENV_FILE" down
    docker-compose --env-file "$ENV_FILE" up -d
    log_info "Rollback completed. Please verify the application."
}

# Scale services
scale() {
    local backend_replicas=${1:-2}
    log_info "Scaling backend service to $backend_replicas replicas..."
    docker-compose --env-file "$ENV_FILE" up -d --scale backend="$backend_replicas"
    log_success "Scaling completed successfully!"
}

# Show logs
logs() {
    local service=${1:-""}
    local lines=${2:-100}
    
    if [ -n "$service" ]; then
        log_info "Showing last $lines lines of logs for service: $service"
        docker-compose --env-file "$ENV_FILE" logs --tail="$lines" "$service"
    else
        log_info "Showing last $lines lines of logs for all services"
        docker-compose --env-file "$ENV_FILE" logs --tail="$lines"
    fi
}

# Show status
status() {
    log_info "BaliBlissed production environment status:"
    docker-compose --env-file "$ENV_FILE" ps
    echo
    log_info "Resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    # Check backend health
    if curl -f -s http://localhost:8000/health > /dev/null; then
        log_success "Backend is healthy"
    else
        log_error "Backend health check failed"
        return 1
    fi
    
    # Check Redis health
    if docker-compose --env-file "$ENV_FILE" exec -T redis redis-cli ping | grep -q "PONG"; then
        log_success "Redis is healthy"
    else
        log_error "Redis health check failed"
        return 1
    fi
    
    return 0
}

# Backup Redis data
backup() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    log_info "Creating backup in $backup_dir..."
    
    mkdir -p "$backup_dir"
    
    # Backup Redis data
    docker-compose --env-file "$ENV_FILE" exec -T redis redis-cli BGSAVE
    sleep 5
    docker cp "$(docker-compose --env-file "$ENV_FILE" ps -q redis)":/data/dump.rdb "$backup_dir/"
    
    # Backup configuration
    cp "$ENV_FILE" "$backup_dir/"
    cp docker-compose.yml "$backup_dir/"
    
    log_success "Backup created successfully in $backup_dir"
}

# Monitor services
monitor() {
    log_info "Monitoring BaliBlissed production environment (Press Ctrl+C to stop)..."
    while true; do
        clear
        echo "=== BaliBlissed Production Monitor ==="
        echo "Time: $(date)"
        echo
        status
        echo
        health_check
        sleep 30
    done
}

# Show help
help() {
    echo "BaliBlissed Production Docker Management Script"
    echo
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo
    echo "Commands:"
    echo "  deploy    Deploy the production environment"
    echo "  stop      Stop the production environment"
    echo "  update    Update services with zero downtime"
    echo "  rollback  Rollback to previous version"
    echo "  scale     Scale backend service (default: 2 replicas)"
    echo "  logs      Show logs (optional: service name, lines)"
    echo "  status    Show status and resource usage"
    echo "  health    Perform health check"
    echo "  backup    Create backup of Redis data and configuration"
    echo "  monitor   Monitor services in real-time"
    echo "  help      Show this help message"
    echo
    echo "Examples:"
    echo "  $0 deploy"
    echo "  $0 scale 3"
    echo "  $0 logs backend 200"
    echo "  $0 backup"
}

# Main script logic
case "${1:-help}" in
    deploy)
        deploy
        ;;
    stop)
        stop
        ;;
    update)
        update
        ;;
    rollback)
        rollback
        ;;
    scale)
        scale "$2"
        ;;
    logs)
        logs "$2" "$3"
        ;;
    status)
        status
        ;;
    health)
        health_check
        ;;
    backup)
        backup
        ;;
    monitor)
        monitor
        ;;
    help|--help|-h)
        help
        ;;
    *)
        log_error "Unknown command: $1"
        help
        exit 1
        ;;
esac
