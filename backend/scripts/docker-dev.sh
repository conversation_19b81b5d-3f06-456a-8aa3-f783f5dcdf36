#!/bin/bash
# scripts/docker-dev.sh
# Development Docker management script for BaliBlissed

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.yml"
ENV_FILE="../.env"
PROJECT_NAME="baliblissed"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Check if environment file exists
check_env() {
    if [ ! -f "$ENV_FILE" ]; then
        log_warning "Environment file $ENV_FILE not found."
        if [ -f "../.env.development.example" ]; then
            log_info "Copying .env.development.example to $ENV_FILE"
            cp ../.env.development.example "$ENV_FILE"
            log_warning "Please update $ENV_FILE with your actual configuration values."
        else
            log_error "No environment template found. Please create $ENV_FILE manually."
            exit 1
        fi
    fi
}

# Create required directories
create_directories() {
    log_info "Creating required directories..."
    mkdir -p ../data/redis ../logs
    log_success "Directories created successfully"
}

# Start development environment
start() {
    log_info "Starting BaliBlissed development environment..."
    check_docker
    check_env
    create_directories
    
    docker-compose --profile development up --build -d
    
    log_success "Development environment started successfully!"
    log_info "Services:"
    log_info "  - Backend API: http://localhost:8000"
    log_info "  - API Docs: http://localhost:8000/docs"
    log_info "  - Redis Commander: http://localhost:8081"
    log_info "  - Health Check: http://localhost:8000/health"
}

# Stop development environment
stop() {
    log_info "Stopping BaliBlissed development environment..."
    docker-compose --profile development down
    log_success "Development environment stopped successfully!"
}

# Restart development environment
restart() {
    log_info "Restarting BaliBlissed development environment..."
    stop
    start
}

# Show logs
logs() {
    local service=${1:-""}
    if [ -n "$service" ]; then
        log_info "Showing logs for service: $service"
        docker-compose --profile development logs -f "$service"
    else
        log_info "Showing logs for all services"
        docker-compose --profile development logs -f
    fi
}

# Show status
status() {
    log_info "BaliBlissed development environment status:"
    docker-compose --profile development ps
    echo
    log_info "Resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

# Clean up everything
clean() {
    log_warning "This will remove all containers, volumes, and images. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Cleaning up BaliBlissed development environment..."
        docker-compose --profile development down -v --remove-orphans --rmi all
        docker system prune -f
        log_success "Cleanup completed successfully!"
    else
        log_info "Cleanup cancelled."
    fi
}

# Reset environment
reset() {
    log_info "Resetting BaliBlissed development environment..."
    docker-compose down -v
    create_directories
    docker-compose up --build -d
    log_success "Environment reset successfully!"
}

# Run tests
test() {
    log_info "Running tests in development environment..."
    docker-compose exec backend python -m pytest tests/ -v
}

# Open shell in backend container
shell() {
    log_info "Opening shell in backend container..."
    docker-compose exec backend bash
}

# Redis CLI
redis() {
    log_info "Opening Redis CLI..."
    docker-compose exec redis redis-cli
}

# Show help
help() {
    echo "BaliBlissed Development Docker Management Script"
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  start     Start the development environment"
    echo "  stop      Stop the development environment"
    echo "  restart   Restart the development environment"
    echo "  logs      Show logs (optional: specify service name)"
    echo "  status    Show status and resource usage"
    echo "  clean     Clean up all containers, volumes, and images"
    echo "  reset     Reset the environment (rebuild containers)"
    echo "  test      Run tests in the backend container"
    echo "  shell     Open bash shell in backend container"
    echo "  redis     Open Redis CLI"
    echo "  help      Show this help message"
    echo
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs backend"
    echo "  $0 status"
}

# Main script logic
case "${1:-help}" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    logs)
        logs "$2"
        ;;
    status)
        status
        ;;
    clean)
        clean
        ;;
    reset)
        reset
        ;;
    test)
        test
        ;;
    shell)
        shell
        ;;
    redis)
        redis
        ;;
    help|--help|-h)
        help
        ;;
    *)
        log_error "Unknown command: $1"
        help
        exit 1
        ;;
esac
