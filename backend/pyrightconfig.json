{"include": ["app"], "exclude": ["**/__pycache__", "**/.pytest_cache", "**/node_modules", ".venv-local", "docs"], "venvPath": ".", "venv": ".venv", "pythonVersion": "3.13", "pythonPlatform": "Linux", "typeCheckingMode": "basic", "useLibraryCodeForTypes": true, "autoSearchPaths": true, "extraPaths": ["."], "reportMissingImports": "warning", "reportMissingTypeStubs": false, "reportImportCycles": "warning", "reportUnusedImport": "information", "reportUnusedClass": "information", "reportUnusedFunction": "information", "reportUnusedVariable": "information", "reportDuplicateImport": "warning", "reportOptionalSubscript": "warning", "reportOptionalMemberAccess": "warning", "reportOptionalCall": "warning", "reportOptionalIterable": "warning", "reportOptionalContextManager": "warning", "reportOptionalOperand": "warning", "reportTypedDictNotRequiredAccess": "warning"}