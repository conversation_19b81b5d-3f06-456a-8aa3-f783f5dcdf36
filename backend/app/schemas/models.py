"""Pydantic models for request and response validation.

This module defines the Pydantic models used for validating
request and response data throughout the BaliBlissed application.
"""

from re import sub

from pydantic import BaseModel, EmailStr, Field, field_validator


class ItineraryRequest(BaseModel):
    """Model for travel itinerary generation requests."""

    destination: str = Field(
        ...,
        min_length=1,
        max_length=100,  # Using constant from settings
        description="The travel destination",
        example="Bali, Indonesia",
    )
    duration: int = Field(
        ...,
        ge=1,  # Using constant from settings
        le=365,  # Using constant from settings
        description="The duration of the trip in days",
        example=7,
    )
    interests: list[str] = Field(
        ...,
        min_length=1,
        max_length=20,  # Using constant from settings
        description="A list of traveler's interests",
        example=["beaches", "temples", "food", "culture"],
    )
    budget: str = Field(
        ...,
        description="The traveler's budget",
        example="$1000",
    )

    @field_validator("destination")
    @classmethod
    def validate_destination(cls, v: str) -> str:
        """Validate and sanitize destination input."""
        if not v or not v.strip():
            msg = "Destination cannot be empty"
            raise ValueError(msg)
        # Remove potentially harmful characters
        sanitized = sub(r'[<>"\']', "", v.strip())
        if len(sanitized) < 1:
            msg = "Destination must contain valid characters"
            raise ValueError(msg)
        return sanitized

    @field_validator("interests")
    @classmethod
    def validate_interests(cls, v: list[str]) -> list[str]:
        """Validate and sanitize interests list."""
        if not v:
            msg = "At least one interest must be provided"
            raise ValueError(msg)

        sanitized_interests = []
        for interest in v:
            if isinstance(interest, str) and interest.strip():
                # Remove potentially harmful characters and limit length
                sanitized = sub(r'[<>"\']', "", interest.strip())[:50]
                if sanitized:
                    sanitized_interests.append(sanitized)

        if not sanitized_interests:
            msg = "At least one valid interest must be provided"
            raise ValueError(msg)

        return sanitized_interests

    @field_validator("budget")
    @classmethod
    def validate_budget(cls, v: str) -> str:
        """Validate budget input."""
        if not v or not v.strip():
            msg = "Budget cannot be empty"
            raise ValueError(msg)
        return v


class ChatMessage(BaseModel):
    """Model for individual chat messages in conversation history."""

    role: str = Field(
        ...,
        description="The role of the message sender",
        example="user",
    )
    parts: list[dict[str, str]] = Field(
        ...,
        description="The message content parts",
        example=[{"text": "Hello, how are you?"}],
    )

    @field_validator("role")
    @classmethod
    def validate_role(cls, v: str) -> str:
        """Validate message role."""
        allowed_roles = {"user", "assistant", "system"}
        if v not in allowed_roles:
            msg = f"Role must be one of: {allowed_roles}"
            raise ValueError(msg)
        return v


class QueryRequest(BaseModel):
    """Model for user query requests."""

    query: str = Field(
        ...,
        min_length=1,
        max_length=1000,  # Using constant from settings
        description="The user's query",
        example="What are the best beaches in Bali?",
    )
    history: list[ChatMessage] = Field(
        default=[],
        max_length=50,  # Limit conversation history
        description="The chat history",
    )

    @field_validator("query")
    @classmethod
    def validate_query(cls, v: str) -> str:
        """Validate and sanitize query input."""

        if not v or not v.strip():
            msg = "Query cannot be empty"
            raise ValueError(msg)

        # Remove potentially harmful characters
        sanitized = sub(r'[<>"\']', "", v.strip())
        if len(sanitized) < 1:
            msg = "Query must contain valid characters"
            raise ValueError(msg)
        return sanitized


class ContactInquiryRequest(BaseModel):
    """Model for contact form submissions."""

    name: str = Field(
        ...,
        min_length=1,
        max_length=100,  # Using constant from settings
        description="The user's name",
        example="John Doe",
    )
    email: EmailStr = Field(
        ...,
        description="The user's email address",
        example="<EMAIL>",
    )
    message: str = Field(
        ...,
        min_length=10,  # Using constant from settings
        max_length=2000,  # Using constant from settings
        description="The user's message",
        example="I would like to know more about your services.",
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate and sanitize name input."""
        if not v or not v.strip():
            msg = "Name cannot be empty"
            raise ValueError(msg)
        # Remove potentially harmful characters
        sanitized = sub(r'[<>"\']', "", v.strip())
        if len(sanitized) < 1:
            msg = "Name must contain valid characters"
            raise ValueError(msg)
        return sanitized

    @field_validator("message")
    @classmethod
    def validate_message(cls, v: str) -> str:
        """Validate and sanitize message input."""
        if not v or not v.strip():
            msg = "Message cannot be empty"
            raise ValueError(msg)
        # Remove potentially harmful characters but preserve basic formatting
        sanitized = sub(r"[<>]", "", v.strip())
        if len(sanitized) < 10:  # Using constant from settings
            msg = "Message must be at least 10 characters long"
            raise ValueError(msg)
        return sanitized


# --- Response Models ---


class ErrorResponse(BaseModel):
    """Standard error response model."""

    error: str = Field(..., description="Error message")
    detail: str | None = Field(None, description="Additional error details")
    timestamp: str = Field(..., description="Error timestamp")


class ItineraryResponse(BaseModel):
    """Response model for itinerary generation."""

    itinerary: str = Field(..., description="Generated travel itinerary")


class QueryResponse(BaseModel):
    """Response model for query processing."""

    answer: str = Field(..., description="AI-generated response to the query")


class ContactAnalysisResponse(BaseModel):
    """Response model for contact inquiry analysis."""

    confirmation: str = Field(..., description="Confirmation message for the user")
