"""Custom exception classes for the BaliBlissed application.

This module defines custom exception classes used throughout the application
to handle specific error conditions related to AI generation and processing.
"""


class AIGenerationError(Exception):
    """Raised when AI content generation fails."""

    def __init__(self, message: str, original_error: Exception | None = None) -> None:
        """Initialize AIGenerationError.

        Args:
            message: Error message describing the failure
            original_error: The original exception that caused this error

        """
        super().__init__(message)
        self.original_error = original_error


class ItineraryGenerationError(AIGenerationError):
    """Raised when AI itinerary generation fails."""


class QueryProcessingError(AIGenerationError):
    """Raised when AI query processing fails."""


class ContactAnalysisError(AIGenerationError):
    """Raised when AI contact analysis fails."""
