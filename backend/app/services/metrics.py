"""Metrics and monitoring service for tracking API performance.

This module provides metrics collection and monitoring capabilities
for tracking API performance, request counts, and error rates.
"""

from collections import defaultdict
from logging import getLogger
from time import time
from types import TracebackType
from typing import Any

from psutil import cpu_percent, disk_usage, virtual_memory

logger = getLogger(__name__)


class MetricsCollector:
    """Collect and track API metrics."""

    def __init__(self) -> None:
        """Initialize metrics collector."""
        self.request_counts: dict[str, int] = defaultdict(int)
        self.error_counts: dict[str, int] = defaultdict(int)
        self.response_times: dict[str, list[float]] = defaultdict(list)
        self.ai_request_counts: dict[str, int] = defaultdict(int)
        self.cache_hits: int = 0
        self.cache_misses: int = 0
        self.circuit_breaker_opens: int = 0
        self.rate_limit_hits: int = 0

    def record_request(self, endpoint: str) -> None:
        """Record an API request.

        Args:
            endpoint: API endpoint path

        """
        self.request_counts[endpoint] += 1

    def record_error(self, endpoint: str) -> None:
        """Record an API error.

        Args:
            endpoint: API endpoint path

        """
        self.error_counts[endpoint] += 1

    def record_response_time(self, endpoint: str, duration: float) -> None:
        """Record response time for an endpoint.

        Args:
            endpoint: API endpoint path
            duration: Response time in seconds

        """
        self.response_times[endpoint].append(duration)

        # Keep only last 1000 entries per endpoint
        if len(self.response_times[endpoint]) > 1000:
            self.response_times[endpoint] = self.response_times[endpoint][-1000:]

    def record_ai_request(self, request_type: str) -> None:
        """Record an AI API request.

        Args:
            request_type: Type of AI request (itinerary, query, contact)

        """
        self.ai_request_counts[request_type] += 1

    def record_cache_hit(self) -> None:
        """Record a cache hit."""
        self.cache_hits += 1

    def record_cache_miss(self) -> None:
        """Record a cache miss."""
        self.cache_misses += 1

    def record_circuit_breaker_open(self) -> None:
        """Record a circuit breaker opening."""
        self.circuit_breaker_opens += 1

    def record_rate_limit_hit(self) -> None:
        """Record a rate limit hit."""
        self.rate_limit_hits += 1

    def get_metrics(self) -> dict[str, Any]:
        """Get current metrics summary.

        Returns:
            Dictionary containing all metrics

        """
        avg_response_times = {
            endpoint: sum(times) / len(times)
            for endpoint, times in self.response_times.items()
            if times
        }
        # Calculate cache hit rate
        total_cache_requests = self.cache_hits + self.cache_misses
        cache_hit_rate = (
            (self.cache_hits / total_cache_requests * 100)
            if total_cache_requests > 0
            else 0.0
        )

        return {
            "request_counts": dict(self.request_counts),
            "error_counts": dict(self.error_counts),
            "avg_response_times": avg_response_times,
            "ai_request_counts": dict(self.ai_request_counts),
            "cache_stats": {
                "hits": self.cache_hits,
                "misses": self.cache_misses,
                "hit_rate": f"{cache_hit_rate:.2f}%",
            },
            "circuit_breaker_opens": self.circuit_breaker_opens,
            "rate_limit_hits": self.rate_limit_hits,
        }

    def reset_metrics(self) -> None:
        """Reset all metrics."""
        self.request_counts.clear()
        self.error_counts.clear()
        self.response_times.clear()
        self.ai_request_counts.clear()
        self.cache_hits = 0
        self.cache_misses = 0
        self.circuit_breaker_opens = 0
        self.rate_limit_hits = 0
        logger.info("Metrics reset")


# Global metrics collector instance
metrics_collector = MetricsCollector()


class RequestTimer:
    """Context manager for timing requests."""

    def __init__(self, endpoint: str) -> None:
        """Initialize request timer.

        Args:
            endpoint: API endpoint path

        """
        self.endpoint = endpoint
        self.start_time: float = 0.0

    def __enter__(self) -> "RequestTimer":
        """Start timer."""
        self.start_time = time()
        metrics_collector.record_request(self.endpoint)
        return self

    def __exit__(
        self,
        exc_type: type[BaseException] | None,
        _exc_val: BaseException | None,
        _exc_tb: TracebackType | None,
    ) -> None:
        """Stop timer and record metrics."""
        duration = time() - self.start_time
        metrics_collector.record_response_time(self.endpoint, duration)

        if exc_type is not None:
            metrics_collector.record_error(self.endpoint)


async def get_system_metrics() -> dict[str, Any]:
    """Get system-level metrics.

    Returns:
        Dictionary containing system metrics

    """

    try:
        # CPU usage

        # Memory usage
        memory = virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / (1024 * 1024)
        memory_total_mb = memory.total / (1024 * 1024)

        # Disk usage
        disk = disk_usage("/")
        disk_percent = disk.percent

        return {
            "cpu_percent": cpu_percent(interval=1),
            "memory": {
                "percent": memory_percent,
                "used_mb": round(memory_used_mb, 2),
                "total_mb": round(memory_total_mb, 2),
            },
            "disk_percent": disk_percent,
        }

    except Exception:
        logger.exception("Failed to get system metrics")
        return {
            "error": "Failed to collect system metrics",
        }
