"""Rate limiting service for API endpoints.

This module provides rate limiting functionality to prevent API abuse
and ensure fair usage across users.
"""

from collections import defaultdict
from logging import getLogger
from time import time
from typing import Any

from fastapi import HTTPException, Request, Response, status

from app.config.settings import settings

logger = getLogger(__name__)

# In-memory rate limit storage (for development)
# Format: {identifier: [(timestamp, count), ...]}
_rate_limit_storage: dict[str, list[tuple[float, int]]] = defaultdict(list)


class RateLimiter:
    """Rate limiter with sliding window algorithm."""

    def __init__(self) -> None:
        """Initialize rate limiter."""
        self.redis_client = None
        self.use_redis = False

        # Try to initialize Redis if configured
        if settings.REDIS_ENABLED:
            try:
                import redis.asyncio as redis  # noqa: PLC0415

                self.redis_client = redis.Redis(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    db=settings.REDIS_DB,
                    password=settings.REDIS_PASSWORD or None,
                    decode_responses=True,
                    socket_connect_timeout=5,
                )
                self.use_redis = True
                logger.info("Redis rate limiter initialized successfully")
            except ImportError:
                logger.warning(
                    "Redis library not installed, using in-memory rate limiter",
                )
            except (ConnectionError, TimeoutError, OSError) as e:
                logger.warning(
                    f"Failed to initialize Redis rate limiter: {e}, using in-memory",
                )

    async def check_rate_limit(
        self,
        identifier: str,
        max_requests: int,
        window_seconds: int,
    ) -> tuple[bool, dict[str, Any]]:
        """Check if request is within rate limit.

        Args:
            identifier: Unique identifier (IP, user ID, etc.)
            max_requests: Maximum requests allowed in window
            window_seconds: Time window in seconds

        Returns:
            Tuple of (is_allowed, rate_limit_info)

        """
        current_time = time()
        window_start = current_time - window_seconds

        try:
            if self.use_redis and self.redis_client:
                # Redis-based rate limiting
                key = f"rate_limit:{identifier}"

                # Remove old entries
                await self.redis_client.zremrangebyscore(key, 0, window_start)

                # Count requests in current window
                request_count = await self.redis_client.zcard(key)

                if request_count >= max_requests:
                    # Get oldest request time to calculate retry_after
                    oldest = await self.redis_client.zrange(key, 0, 0, withscores=True)
                    retry_after = (
                        int(
                            oldest[0][1] + window_seconds - current_time,
                        )
                        if oldest
                        else window_seconds
                    )

                    return False, {
                        "limit": max_requests,
                        "remaining": 0,
                        "reset": int(current_time + retry_after),
                        "retry_after": retry_after,
                    }

                # Add current request
                await self.redis_client.zadd(key, {str(current_time): current_time})
                await self.redis_client.expire(key, window_seconds)

                return True, {
                    "limit": max_requests,
                    "remaining": max_requests - request_count - 1,
                    "reset": int(current_time + window_seconds),
                }

            else:
                # In-memory fallback
                requests = _rate_limit_storage[identifier]

                # Remove old entries
                requests[:] = [
                    (ts, count) for ts, count in requests if ts > window_start
                ]

                # Count total requests in window
                total_requests = sum(count for _, count in requests)

                if total_requests >= max_requests:
                    # Calculate retry_after
                    oldest_ts = requests[0][0] if requests else current_time
                    retry_after = int(oldest_ts + window_seconds - current_time)

                    return False, {
                        "limit": max_requests,
                        "remaining": 0,
                        "reset": int(current_time + retry_after),
                        "retry_after": retry_after,
                    }

                # Add current request
                requests.append((current_time, 1))

                return True, {
                    "limit": max_requests,
                    "remaining": max_requests - total_requests - 1,
                    "reset": int(current_time + window_seconds),
                }

        except Exception:
            logger.exception("Rate limit check error")
            # On error, allow the request (fail open)
            return True, {
                "limit": max_requests,
                "remaining": max_requests,
                "reset": int(current_time + window_seconds),
            }

    async def cleanup_expired(self) -> None:
        """Clean up expired rate limit entries (for in-memory storage)."""
        if not self.use_redis:
            current_time = time()
            for identifier in list(_rate_limit_storage.keys()):
                requests = _rate_limit_storage[identifier]
                # Keep only recent entries (last hour)
                requests[:] = [
                    (ts, count) for ts, count in requests if ts > current_time - 3600
                ]
                if not requests:
                    del _rate_limit_storage[identifier]

    async def close(self) -> None:
        """Close rate limiter connections."""
        if self.use_redis and self.redis_client:
            await self.redis_client.close()
            logger.info("Redis rate limiter connection closed")


# Global rate limiter instance
rate_limiter = RateLimiter()


async def rate_limit_middleware(
    request: Request,
    max_requests: int | None = None,
    window_seconds: int | None = None,
) -> None:
    """Rate limit middleware for FastAPI endpoints.

    Args:
        request: FastAPI request object
        max_requests: Maximum requests allowed (uses settings default if None)
        window_seconds: Time window in seconds (uses settings default if None)

    Raises:
        HTTPException: If rate limit exceeded

    """
    # Use defaults from settings if not specified
    max_requests = max_requests or settings.RATE_LIMIT_REQUESTS
    window_seconds = window_seconds or settings.RATE_LIMIT_WINDOW

    # Get identifier (IP address or user ID)
    identifier = request.client.host if request.client else "unknown"

    # Check rate limit
    is_allowed, rate_info = await rate_limiter.check_rate_limit(
        identifier,
        max_requests,
        window_seconds,
    )

    # Add rate limit headers to response
    request.state.rate_limit_info = rate_info

    if not is_allowed:
        logger.warning(
            f"Rate limit exceeded for {identifier} on {request.url.path}",
        )
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Rate limit exceeded. Try again in {rate_info['retry_after']} seconds.",
            headers={
                "X-RateLimit-Limit": str(rate_info["limit"]),
                "X-RateLimit-Remaining": str(rate_info["remaining"]),
                "X-RateLimit-Reset": str(rate_info["reset"]),
                "Retry-After": str(rate_info["retry_after"]),
            },
        )


def add_rate_limit_headers(response: Response, rate_info: dict[str, Any]) -> None:
    """Add rate limit headers to response.

    Args:
        response: FastAPI response object
        rate_info: Rate limit information

    """
    response.headers["X-RateLimit-Limit"] = str(rate_info.get("limit", 0))
    response.headers["X-RateLimit-Remaining"] = str(rate_info.get("remaining", 0))
    response.headers["X-RateLimit-Reset"] = str(rate_info.get("reset", 0))
