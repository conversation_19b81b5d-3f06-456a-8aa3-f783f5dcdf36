"""Caching service for AI responses and frequently accessed data.

This module provides caching functionality using Redis for distributed caching
and in-memory fallback for development environments.
"""

from hashlib import sha256
from json import dumps
from logging import getLogger
from time import time
from typing import Any

from app.config.settings import settings

logger = getLogger(__name__)

# In-memory cache fallback (for development)
_memory_cache: dict[str, tuple[str, float]] = {}
_cache_ttl: dict[str, int] = {
    "itinerary": 3600 * 24,  # 24 hours
    "query": 3600,  # 1 hour
    "contact": 1800,  # 30 minutes
}


class CacheService:
    """Cache service with Redis and in-memory fallback."""

    def __init__(self) -> None:
        """Initialize cache service."""
        self.redis_client = None
        self.use_redis = False

        # Try to initialize Redis if configured
        if settings.REDIS_ENABLED:
            try:
                import redis.asyncio as redis  # noqa: PLC0415

                self.redis_client = redis.Redis(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    db=settings.REDIS_DB,
                    password=settings.REDIS_PASSWORD or None,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_keepalive=True,
                    health_check_interval=30,
                )
                self.use_redis = True
                logger.info("Redis cache initialized successfully")
            except ImportError:
                logger.warning("Redis library not installed, using in-memory cache")
            except (ConnectionError, TimeoutError, OSError) as e:
                logger.warning(
                    f"Failed to initialize Redis: {e}, using in-memory cache",
                )

    async def get(self, key: str) -> str | None:
        """Get value from cache.

        Args:
            key: Cache key

        Returns:
            Cached value or None if not found

        """
        try:
            if self.use_redis and self.redis_client:
                value = await self.redis_client.get(key)
                if value:
                    logger.debug(f"Cache hit (Redis): {key}")
                    return value
            elif key in _memory_cache:
                value, _ = _memory_cache[key]
                logger.debug(f"Cache hit (memory): {key}")
                return value

            logger.debug(f"Cache miss: {key}")

        except Exception:
            logger.exception("Cache get error")

    async def set(
        self,
        key: str,
        value: str,
        ttl: int | None = None,
        cache_type: str = "query",
    ) -> bool:
        """Set value in cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (optional)
            cache_type: Type of cache for default TTL

        Returns:
            True if successful, False otherwise

        """
        try:
            if ttl is None:
                ttl = _cache_ttl.get(cache_type, 3600)

            if self.use_redis and self.redis_client:
                await self.redis_client.setex(key, ttl, value)
                logger.debug(f"Cache set (Redis): {key} (TTL: {ttl}s)")
            else:
                # In-memory fallback

                _memory_cache[key] = (value, time() + ttl)
                logger.debug(f"Cache set (memory): {key} (TTL: {ttl}s)")

        except Exception:
            logger.exception("Cache set error")
            return False

        return True

    async def delete(self, key: str) -> bool:
        """Delete value from cache.

        Args:
            key: Cache key

        Returns:
            True if successful, False otherwise

        """
        try:
            if self.use_redis and self.redis_client:
                await self.redis_client.delete(key)
            else:
                _memory_cache.pop(key, None)

            logger.debug(f"Cache delete: {key}")

        except Exception:
            logger.exception("Cache delete error")
            return False

        return True

    async def clear_expired(self) -> None:
        """Clear expired entries from in-memory cache."""
        if not self.use_redis:
            current_time = time()
            expired_keys = [
                key
                for key, (_, expiry) in _memory_cache.items()
                if expiry < current_time
            ]
            for key in expired_keys:
                _memory_cache.pop(key, None)

            if expired_keys:
                logger.debug(f"Cleared {len(expired_keys)} expired cache entries")

    async def close(self) -> None:
        """Close cache connections."""
        if self.use_redis and self.redis_client:
            await self.redis_client.close()
            logger.info("Redis cache connection closed")


def generate_cache_key(prefix: str, data: dict[str, Any]) -> str:
    """Generate a cache key from request data.

    Args:
        prefix: Key prefix (e.g., 'itinerary', 'query')
        data: Request data to hash

    Returns:
        Cache key string

    """
    # Sort and serialize data for consistent hashing
    serialized = dumps(data, sort_keys=True)
    hash_value = sha256(serialized.encode()).hexdigest()[:16]
    return f"{prefix}:{hash_value}"


# Global cache instance
cache_service = CacheService()
