"""Circuit breaker pattern implementation for external service calls.

This module provides circuit breaker functionality to prevent cascading failures
when external services (like AI APIs) are experiencing issues.
"""

from asyncio import sleep
from collections.abc import Callable
from enum import Enum
from logging import getLogger
from time import time
from typing import Any

logger = getLogger(__name__)


class CircuitState(Enum):
    """Circuit breaker states."""

    CLOSED = "closed"  # Normal operation
    OPEN = "open"  # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing if service recovered


class CircuitBreakerError(Exception):
    """Raised when circuit breaker is open."""


class CircuitBreaker:
    """Circuit breaker for external service calls."""

    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: type[Exception] = Exception,
        name: str = "default",
    ) -> None:
        """Initialize circuit breaker.

        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds to wait before attempting recovery
            expected_exception: Exception type to catch
            name: Circuit breaker name for logging

        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        self.name = name

        self.failure_count = 0
        self.last_failure_time: float | None = None
        self.state = CircuitState.CLOSED

    async def call(
        self,
        func: Callable,
        *args: list[Any],
        **kwargs: dict[str, Any],
    ) -> Any:  # noqa: ANN401
        """Execute function with circuit breaker protection.

        Args:
            func: Async function to execute
            *args: Positional arguments for function
            **kwargs: Keyword arguments for function

        Returns:
            Function result

        Raises:
            CircuitBreakerError: If circuit is open
            Exception: Original exception if circuit is closed

        """
        # Check if circuit should transition to half-open
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
                logger.info(f"Circuit breaker '{self.name}' entering HALF_OPEN state")
            else:
                time_remaining = self._time_until_reset()
                logger.warning(
                    f"Circuit breaker '{self.name}' is OPEN. "
                    f"Retry in {time_remaining:.1f}s",
                )
                mssg = (
                    f"Service temporarily unavailable. Retry in {time_remaining:.1f}s"
                )
                err_mssg = f"Circuit breaker '{self.name}' is open. {mssg}"
                logger.warning(mssg)
                raise CircuitBreakerError(err_mssg)

        try:
            # Execute the function
            result = await func(*args, **kwargs)

            # Success - reset failure count
            if self.state == CircuitState.HALF_OPEN:
                self._on_success()
                logger.info(f"Circuit breaker '{self.name}' recovered, now CLOSED")

            self.failure_count = 0

        except self.expected_exception:
            # Failure - increment counter
            self._on_failure()
            logger.exception(
                f"Circuit breaker '{self.name}' failure {self.failure_count}/"
                f"{self.failure_threshold}",
            )
            raise

        return result

    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset.

        Returns:
            True if should attempt reset

        """
        if self.last_failure_time is None:
            return True

        return time() - self.last_failure_time >= self.recovery_timeout

    def _time_until_reset(self) -> float:
        """Calculate time remaining until reset attempt.

        Returns:
            Seconds until reset

        """
        if self.last_failure_time is None:
            return 0.0

        elapsed = time() - self.last_failure_time
        return max(0.0, self.recovery_timeout - elapsed)

    def _on_success(self) -> None:
        """Handle successful call."""
        self.failure_count = 0
        self.state = CircuitState.CLOSED

    def _on_failure(self) -> None:
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = time()

        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
            logger.error(
                f"Circuit breaker '{self.name}' opened after "
                f"{self.failure_count} failures",
            )

    def reset(self) -> None:
        """Manually reset circuit breaker."""
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
        logger.info(f"Circuit breaker '{self.name}' manually reset")

    def get_state(self) -> dict[str, Any]:
        """Get current circuit breaker state.

        Returns:
            Dictionary with state information

        """
        return {
            "name": self.name,
            "state": self.state.value,
            "failure_count": self.failure_count,
            "failure_threshold": self.failure_threshold,
            "last_failure_time": self.last_failure_time,
            "time_until_reset": self._time_until_reset()
            if self.state == CircuitState.OPEN
            else 0,
        }


# Global circuit breakers for different services
ai_circuit_breaker = CircuitBreaker(
    failure_threshold=5,
    recovery_timeout=60,
    expected_exception=Exception,
    name="gemini_ai",
)

email_circuit_breaker = CircuitBreaker(
    failure_threshold=3,
    recovery_timeout=30,
    expected_exception=Exception,
    name="email_service",
)


async def with_retry(
    func: Callable,
    max_retries: int = 3,
    retry_delay: float = 1.0,
    backoff_factor: float = 2.0,
    *args: list[Any],
    **kwargs: dict[str, Any],
) -> Any:  # noqa: ANN401
    """Execute function with exponential backoff retry.

    Args:
        func: Async function to execute
        max_retries: Maximum number of retry attempts
        retry_delay: Initial delay between retries in seconds
        backoff_factor: Multiplier for delay after each retry
        *args: Positional arguments for function
        **kwargs: Keyword arguments for function

    Returns:
        Function result

    Raises:
        Exception: Last exception if all retries fail

    """
    last_exception = None
    delay = retry_delay

    for attempt in range(max_retries + 1):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            last_exception = e

            if attempt < max_retries:
                logger.warning(
                    f"Attempt {attempt + 1}/{max_retries + 1} failed: {last_exception}. Retrying in {delay:.1f}s...",
                )
                await sleep(delay)
                delay *= backoff_factor
            else:
                logger.exception(f"All {max_retries + 1} attempts failed")

    raise last_exception  # type: ignore
