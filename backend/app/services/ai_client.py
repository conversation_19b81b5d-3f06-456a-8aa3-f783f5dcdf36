"""AI client with connection pooling, timeout handling, and optimization.

This module provides an optimized AI client for Google Gemini API with
connection pooling, timeout handling, retry logic, and circuit breaker protection.
"""

from asyncio import Lock, wait_for
from logging import getLogger
from typing import Any

import google.generativeai as genai

from app.config.settings import (
    GEMINI_MODEL,
    GENERATION_CONFIG,
    SAFETY_SETTINGS,
    settings,
)
from app.services.circuit_breaker import ai_circuit_breaker, with_retry

logger = getLogger(__name__)


class AIClient:
    """Optimized AI client with connection pooling and error handling."""

    def __init__(self) -> None:
        """Initialize AI client."""
        self.model = None
        self.initialized = False
        self._lock = Lock()

    async def initialize(self) -> None:
        """Initialize AI model (lazy initialization)."""
        if self.initialized:
            return

        async with self._lock:
            if self.initialized:
                return

            try:
                # Configure API key
                genai.configure(api_key=settings.GEMINI_API_KEY)  # type: ignore[attr-defined]

                # Create model instance (reusable)
                self.model = genai.GenerativeModel(  # type: ignore[attr-defined]
                    model_name=GEMINI_MODEL,
                    generation_config=GENERATION_CONFIG,
                    safety_settings=SAFETY_SETTINGS,
                )

                self.initialized = True
                logger.info("AI client initialized successfully")

            except Exception as e:
                logger.exception("Failed to initialize AI client")
                raise RuntimeError("AI client initialization failed") from e

    async def generate_content(
        self,
        prompt: str,
    ) -> str:
        """Generate content using AI with timeout and retry.

        Args:
            prompt: Input prompt for AI
            timeout: Timeout in seconds
            use_cache: Whether to use caching (for future implementation)

        Returns:
            Generated content

        Raises:
            Exception: If generation fails after retries

        """
        # Ensure client is initialized
        await self.initialize()

        # Define the actual generation function
        async def _generate() -> str:
            try:
                # Use asyncio.wait_for for timeout
                response = await wait_for(
                    self.model.generate_content_async(prompt),
                    timeout=settings.AI_REQUEST_TIMEOUT,
                )

                if not response.text:
                    msg = "AI model returned empty response"
                    raise ValueError(msg)

            except TimeoutError as e:
                logger.exception(
                    f"AI generation timeout after {settings.AI_REQUEST_TIMEOUT}s",
                )
                msg = (
                    f"AI request timed out after {settings.AI_REQUEST_TIMEOUT} seconds"
                )
                raise TimeoutError(msg) from e

            return response.text

        # Execute with circuit breaker and retry
        try:
            result = await with_retry(
                _generate,
                max_retries=2,
                retry_delay=1.0,
                backoff_factor=2.0,
            )

        except Exception:
            logger.exception("AI generation failed")
            raise

        return result

    async def generate_chat_response(
        self,
        message: str,
        history: list[dict[str, Any]] | None = None,
    ) -> str:
        """Generate chat response with conversation history.

        Args:
            message: User message
            history: Conversation history
            timeout: Timeout in seconds

        Returns:
            AI response

        Raises:
            Exception: If generation fails

        """
        # Ensure client is initialized
        await self.initialize()

        async def _generate_chat() -> str:
            try:
                if history:
                    # Continue existing conversation
                    chat = self.model.start_chat(history=history)
                    response = await wait_for(
                        chat.send_message_async(message),
                        timeout=settings.AI_REQUEST_TIMEOUT,
                    )
                else:
                    # New conversation
                    response = await wait_for(
                        self.model.generate_content_async(message),
                        timeout=settings.AI_REQUEST_TIMEOUT,
                    )

                if not response.text:
                    msg = "AI model returned empty response"
                    raise ValueError(msg)

            except TimeoutError as e:
                logger.exception(
                    f"AI chat generation timeout after {settings.AI_REQUEST_TIMEOUT}s"
                )
                msg = (
                    f"AI request timed out after {settings.AI_REQUEST_TIMEOUT} seconds"
                )
                raise TimeoutError(msg) from e

            return response.text

        # Execute with circuit breaker and retry
        try:
            result = await with_retry(
                _generate_chat,
                max_retries=2,
                retry_delay=1.0,
                backoff_factor=2.0,
            )

        except Exception:
            logger.exception("AI chat generation failed")
            raise

        return result


# Global AI client instance (singleton)
ai_client = AIClient()
