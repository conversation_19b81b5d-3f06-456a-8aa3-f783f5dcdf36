"""Background task processing for long-running operations.

This module provides background task processing capabilities for operations
that don't need to block the HTTP response, such as email sending and analytics.
"""

from asyncio import (
    CancelledError,
    Queue,
    Task,
    create_task,
    gather,
    iscoroutinefunction,
)
from collections.abc import Callable
from logging import getLogger
from typing import Any

logger = getLogger(__name__)


class TaskQueue:
    """Simple in-memory task queue for background processing."""

    def __init__(self, max_workers: int = 5) -> None:
        """Initialize task queue.

        Args:
            max_workers: Maximum number of concurrent workers

        """
        self.max_workers = max_workers
        self.queue: Queue = Queue()
        self.workers: list[Task] = []
        self.running = False

    async def start(self) -> None:
        """Start background workers."""
        if self.running:
            return

        self.running = True
        self.workers = [create_task(self._worker(i)) for i in range(self.max_workers)]
        logger.info(f"Started {self.max_workers} background task workers")

    async def stop(self) -> None:
        """Stop background workers."""
        if not self.running:
            return

        self.running = False

        # Wait for queue to be empty
        await self.queue.join()

        # Cancel all workers
        for worker in self.workers:
            worker.cancel()

        # Wait for workers to finish
        await gather(*self.workers, return_exceptions=True)

        logger.info("Stopped all background task workers")

    async def _worker(self, worker_id: int) -> None:
        """Background worker that processes tasks from queue.

        Args:
            worker_id: Worker identifier

        """
        logger.info(f"Worker {worker_id} started")

        while self.running:
            try:
                # Get task from queue
                task_func, args, kwargs = await self.queue.get()

                try:
                    # Execute task
                    if iscoroutinefunction(task_func):
                        await task_func(*args, **kwargs)
                    else:
                        task_func(*args, **kwargs)

                    logger.debug(
                        f"Worker {worker_id} completed task: {task_func.__name__}",
                    )

                except Exception:
                    logger.exception(
                        f"Worker {worker_id} error executing task {task_func.__name__}",
                    )

                finally:
                    self.queue.task_done()

            except CancelledError:
                logger.info(f"Worker {worker_id} cancelled")
                break
            except Exception:
                logger.exception(f"Worker {worker_id} unexpected error")

    async def add_task(
        self,
        func: Callable,
        *args: list[Any],
        **kwargs: dict[str, Any],
    ) -> None:
        """Add task to queue for background processing.

        Args:
            func: Function to execute
            *args: Positional arguments for function
            **kwargs: Keyword arguments for function

        """
        await self.queue.put((func, args, kwargs))
        logger.debug(f"Added task to queue: {func.__name__}")

    def get_queue_size(self) -> int:
        """Get current queue size.

        Returns:
            Number of tasks in queue

        """
        return self.queue.qsize()


# Global task queue instance
task_queue = TaskQueue(max_workers=5)
