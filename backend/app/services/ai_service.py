"""AI service functions for generating travel itineraries, processing queries, and analyzing contacts.

This module contains all the AI-related business logic for the BaliBlissed application,
including itinerary generation, query processing, and contact inquiry analysis with
caching, retry logic, and circuit breaker protection.
"""

from datetime import datetime
from json import JSONDecodeError, dumps, loads
from logging import getLogger
from typing import Any

from mdformat import text as mdformat_text

from app.config.settings import WHATSAPP_NUMBER, settings
from app.exceptions.ai_exceptions import (
    AIGenerationError,
    ContactAnalysisError,
    ItineraryGenerationError,
    QueryProcessingError,
)
from app.schemas.models import (
    ChatMessage,
    ContactInquiryRequest,
    ItineraryRequest,
    QueryRequest,
)
from app.services.ai_client import ai_client
from app.services.cache_service import cache_service, generate_cache_key

logger = getLogger(__name__)


def create_error_response(message: str, detail: str | None = None) -> dict[str, Any]:
    """Create a standardized error response.

    Args: message: The error message to include in the response
        detail: Additional details about the error (optional)

    Returns: A dictionary containing the error response with timestamp
    """
    return {
        "error": message,
        "detail": detail,
        "timestamp": datetime.now(datetime.now().astimezone().tzinfo).isoformat(),
    }


def create_itinerary_prompt(request: ItineraryRequest) -> str:
    """Create a detailed prompt for itinerary generation.

    Args: request: The itinerary request containing destination, duration, and interests.

    Returns: A formatted prompt string for the AI model.
    """

    interests_text = ", ".join(request.interests)

    format_example = f"""
            # 🌴 3-Days Bali Adventure Itinerary: Budget $1,100 🌴

            This itinerary focuses on adventure and authentic Balinese experiences, staying within your $1,100 budget. Get ready for an unforgettable journey!

            ## 🌅DAILY BREAKDOWN

            ### **Day 1: Ubud's Thrills & Tranquility 🐒**

            **Morning Activity (9-11 AM):** White Water Rafting on the Ayung River 🌊 (approx. $35-$50).

            - **Location:** Ayung River, Ubud. Many rafting companies operate here.
            - **Time:** 9:00 AM - 11:00 AM (allow for transportation to the river).
            - **Details:** Experience the thrill of navigating the rapids through lush rainforest. Most tours include safety briefings and equipment. Book in advance for better deals.
            - **Authentic Experience:** Many rafting companies are locally owned and operated, providing jobs and supporting the community.
            - **Insider Tip:** Wear quick-drying clothes and sunscreen!

            **Afternoon Activity (12-4 PM):** Tegalalang Rice Terraces Exploration & Swing 🌾 (approx. $15-$30 including swing).

            - **Location:** Tegalalang Rice Terraces, north of Ubud.
            - **Time:** 12:00 PM - 4:00 PM (including lunch and exploring).
            - **Lunch:** Warung D'Carik (mid-range, Indonesian cuisine with rice field views) or a local warung within the terraces for a more budget-friendly option. Try Nasi Campur (mixed rice).
            - **Details:** Wander through the iconic rice terraces, take stunning photos, and experience the adrenaline rush of a Balinese swing.
            - **Authentic Experience:** Engage with local farmers and learn about traditional rice farming techniques.
            - **Insider Tip:** Wear comfortable walking shoes as the terraces can be steep and slippery.

            **Evening Activity (5-8 PM):** Traditional Balinese Dance Performance at Ubud Palace 💃 (approx. $10-$15).

            - **Location:** Ubud Palace.
            - **Time:** Performances usually start around 7:30 PM. Arrive early to secure a good spot.
            - **Dinner:** Bebek Tepi Sawah Restaurant (mid-range, famous for crispy duck) or Warung Ibu Oka (budget-friendly, Babi Guling - suckling pig).
            - **Details:** Immerse yourself in Balinese culture with a captivating dance performance showcasing traditional music, costumes, and stories.
            - **Authentic Experience:** The Ubud Palace is a historical landmark and a center for Balinese arts and culture.
            - **Insider Tip:** Check the schedule in advance as performances vary.

            **Evening Wind-down:** Relax at a local cafe with a Bintang beer 🍺 or a fresh juice (approx. $5-$10). Try a cafe near your accommodation in Ubud.

            ### **Day 2: Volcano Views & Sacred Waters 🌋**

            **Morning Activity (9-11 AM):** Sunrise Trek to Mount Batur 🌄 (approx. $40-$60 including guide and transportation).

            - **Location:** Mount Batur, Kintamani.
            - **Time:** Pick-up around 3:00 AM to reach the summit before sunrise. The trek takes approximately 2-3 hours.
            - **Details:** Hike to the summit of Mount Batur, an active volcano, to witness a breathtaking sunrise over the Balinese landscape. A guide is highly recommended for safety and navigation.
            - **Authentic Experience:** Share breakfast cooked on volcanic steam with your guide and fellow trekkers.
            - **Insider Tip:** Wear layers as it can be cold at the summit. Bring a headlamp and sturdy hiking shoes.

            **Afternoon Activity (12-4 PM):** Tirta Empul Temple Visit & Purification Ritual 🙏 (approx. $5-$10 including sarong rental and donation).

            - **Location:** Tirta Empul Temple, Tampaksiring.
            - **Time:** 12:00 PM - 4:00 PM (including travel time from Kintamani and exploring).
            - **Lunch:** A local warung near Tirta Empul Temple. Try Sate Lilit (minced meat satay).
            - **Details:** Participate in a traditional purification ritual at Tirta Empul Temple, a sacred water temple. Bathe in the holy springs and cleanse your body and soul.
            - **Authentic Experience:** Witness the devotion of Balinese Hindus and learn about the significance of the temple.
            - **Insider Tip:** Dress respectfully (shoulders and knees covered). Sarongs and sashes can be rented at the entrance.

            **Evening Activity (5-8 PM):** Kintamani Dinner with Volcano View 🍽️ (approx. $15-$25).

            - **Location:** Restaurants in Kintamani with views of Mount Batur.
            - **Time:** 6:00 PM - 8:00 PM.
            - **Dinner:** Lake View Hotel & Restaurant (mid-range, Indonesian and international cuisine with stunning views) or a local warung for a more affordable option.
            - **Details:** Enjoy a delicious dinner while admiring the majestic Mount Batur volcano and Lake Batur.
            - **Authentic Experience:** The Kintamani region is known for its coffee plantations. Try a cup of local Balinese coffee.
            - **Insider Tip:** The weather in Kintamani can be cooler than in other parts of Bali. Bring a jacket.

            **Evening Wind-down:** Relax and enjoy the peaceful atmosphere of Kintamani with a cup of herbal tea or hot chocolate (approx. $5-$10).

            ### **Day 3: Coastal Adventures & Sunset Serenity 🏖️**

            **Morning Activity (9-11 AM):** Surfing Lesson at Kuta Beach 🏄 (approx. $25-$40).

            - **Location:** Kuta Beach.
            - **Time:** 9:00 AM - 11:00 AM.
            - **Details:** Learn to surf at Kuta Beach, a popular spot for beginners. Many surf schools offer lessons and board rentals.
            - **Authentic Experience:** Interact with local surf instructors and learn about the surfing culture in Bali.
            - **Insider Tip:** Book a lesson with a reputable surf school and wear sunscreen.

            **Afternoon Activity (12-4 PM):** Uluwatu Temple Visit & Kecak Fire Dance 🐒🔥 (approx. $20-$30 including temple entrance and Kecak dance ticket).

            - **Location:** Uluwatu Temple.
            - **Time:** 12:00 PM - 4:00 PM (including travel time from Kuta and exploring).
            - **Lunch:** Warung Made (mid-range, Indonesian cuisine) in Kuta before heading to Uluwatu.
            - **Details:** Visit Uluwatu Temple, a stunning cliffside temple with panoramic ocean views. Watch the Kecak Fire Dance, a traditional Balinese performance depicting scenes from the Ramayana epic.
            - **Authentic Experience:** Uluwatu Temple is one of the most important temples in Bali and a sacred place for Balinese Hindus.
            - **Insider Tip:** Be aware of the monkeys at Uluwatu Temple, as they can be mischievous. Secure your belongings.

            **Evening Activity (5-8 PM):** Sunset Dinner at Jimbaran Bay 🦞 (approx. $30-$50).

            - **Location:** Jimbaran Bay.
            - **Time:** 6:00 PM - 8:00 PM.
            - **Dinner:** Choose a seafood restaurant along Jimbaran Bay. Many restaurants offer fresh seafood grilled on the beach.
            - **Details:** Enjoy a romantic sunset dinner on the beach at Jimbaran Bay, known for its fresh seafood and stunning sunsets.
            - **Authentic Experience:** Choose a restaurant that sources its seafood locally.
            - **Insider Tip:** Negotiate the price of the seafood before ordering.

            **Evening Wind-down:** Enjoy a final Balinese massage (approx. $15-$25) to relax and rejuvenate before your departure.

            ## 🏨 HANDPICKED ACCOMMODATIONS

            1. **Ubud:**
                * **Mid-Range:** Pertiwi Bisma 2 ($40-$60/night): Located in central Ubud, close to attractions and with beautiful pool areas.
                * **Premium (within budget):** The Lokha Ubud Resort, Villas & Spa ($80-$120/night): Offers a luxurious stay with stunning views and excellent amenities.

            2. **Kintamani:**
                * **Mid-Range:** Batur Panorama ($30-$50/night): Offers stunning views of Mount Batur and Lake Batur.
                * **Premium (within budget):**  Lake View Hotel & Restaurant ($50-$80/night): Comfortable rooms with incredible views and on-site dining.

            3. **Kuta/Seminyak (for the last night):**
                * **Mid-Range:** Amnaya Resort Kuta ($50-$70/night): Close to the airport and Kuta Beach, offering a comfortable stay.
                * **Premium (within budget):** The Breezes Bali Resort & Spa ($80-$120/night): A stylish resort with a great pool and beach access in Seminyak (a short taxi ride from Kuta).

            These accommodations are chosen for their proximity to the activities planned, offering a balance of comfort and value within your budget.

            ## 🍜 CULINARY EXPERIENCES

            1. **Daily Restaurant Recommendations:**
                * **Breakfast:**
                    * Day 1: At your Ubud accommodation or a local cafe like Atman Kafe.
                    * Day 2: Included in your Mount Batur trek or at your Kintamani accommodation.
                    * Day 3: At your Kuta/Seminyak accommodation or a cafe like Revolver Espresso.
                * **Lunch:** (See daily breakdown for specific recommendations)
                * **Dinner:** (See daily breakdown for specific recommendations)

            2. **Must-Try Balinese Dishes:**
                * Babi Guling (suckling pig)
                * Bebek Betutu (slow-cooked duck)
                * Nasi Goreng (fried rice)
                * Mie Goreng (fried noodles)
                * Sate Lilit (minced meat satay)
                * Gado-Gado (vegetable salad with peanut sauce)

            3. **Food Market or Cooking Class:**
                * Ubud Traditional Art Market (Pasar Seni Ubud) - explore local snacks and produce.
                * Paon Bali Cooking Class (Ubud) - learn to cook traditional Balinese dishes.

            4. **Beverage Recommendations:**
                * Bintang beer
                * Es Teh Tarik (pulled milk tea)
                * Fresh juices (watermelon, pineapple, mango)
                * Balinese coffee (Kopi Bali)

            ## 🚗 TRANSPORTATION & LOGISTICS

            1. **Getting to/from Airport:**
                * Pre-booked airport transfer: approx. $15-$25.
                * Metered taxi: approx. $10-$20 (ensure the meter is running).
                * Grab/Gojek: often cheaper but may have limited availability at the airport.

            2. **Daily Transportation Options:**
                * **Scooter Rental:** approx. $5-$10/day (requires an international driving permit and caution).
                * **Private Car with Driver:** approx. $40-$60/day (recommended for longer distances and comfort). Book in advance through your hotel or a reputable tour operator.
                * **Taxi/Grab/Gojek:** readily available in tourist areas.

            3. **Estimated Costs & Booking Tips:**
                * Book transportation in advance, especially for airport transfers and Mount Batur trek.
                * Negotiate prices with taxi drivers and scooter rental companies.
                * Use ride-hailing apps like Grab/Gojek for shorter distances.

            ## 🎭 CULTURAL IMMERSION

            1. **Local Customs & Etiquette Tips:**
                * Dress modestly when visiting temples (shoulders and knees covered).
                * Remove your shoes before entering homes and temples.
                * Use your right hand for giving and receiving.
                * Avoid pointing with your finger.
                * Do not touch people's heads, as it is considered sacred.
                * Bargain respectfully when shopping at markets.

            2. **Temple Visit Protocols:**
                * Wear a sarong and sash (usually available for rent at the entrance).
                * Do not enter temples if you are menstruating.
                * Follow the instructions of the temple staff.

            3. **Respectful Photography Guidelines:**
                * Ask for permission before taking photos of people.
                * Avoid using flash photography inside temples.
                * Be respectful of religious ceremonies and processions.

            ## 💸 COMPREHENSIVE BUDGET BREAKDOWN

            1. **Daily Spending Estimates:**
                * Day 1: $35 (Rafting) + $20 (Rice Terraces/Swing) + $15 (Dance) + $10 (Food/Drinks) + $50 (Accommodation) + $20 (Transportation) = $150
                * Day 2: $50 (Batur) + $10 (Temple) + $20 (Dinner) + $10 (Food/Drinks) + $50 (Accommodation) + $30 (Transportation) = $170
                * Day 3: $30 (Surfing) + $25 (Uluwatu) + $40 (Dinner) + $10 (Food/Drinks) + $50 (Accommodation) + $30 (Transportation) + $20 (Massage) = $205

            2. **Total Estimated Cost vs. Declared Budget:**
                * Total Estimated Cost: $150 + $170 + $205 = $525
                * Remaining Budget: $1100 - $525 = $575 (for flights, souvenirs, incidentals)

            3. **Cost-Saving Tips & Premium Upgrade Options:**
                * **Cost-Saving:** Eat at local warungs instead of tourist restaurants. Rent a scooter instead of hiring a private car. Stay in budget-friendly accommodations.
                * **Premium Upgrade:** Upgrade to a luxury villa with a private pool. Hire a private driver for the entire trip. Take a private cooking class. Enjoy a spa treatment.

            ## ⚡ PRACTICAL INFORMATION

            1. **Best Times to Visit Featured Locations:**
                * Ubud: Year-round, but avoid the rainy season (November-March) for outdoor activities.
                * Mount Batur: Dry season (April-October) for the best sunrise views.
                * Kuta Beach: Year-round, but the best surfing conditions are during the dry season.
                * Uluwatu Temple: Year-round, but arrive early for the Kecak Fire Dance to secure a good spot.

            2. **Weather Considerations by Season:**
                * Dry Season (April-October): Sunny and dry, with temperatures ranging from 25°C to 32°C.
                * Rainy Season (November-March): Humid and rainy, with temperatures ranging from 24°C to 30°C.

            3. **Emergency Contacts:**
                * Police: 110
                * Ambulance: 118
                * Fire Department: 113
                * U.S. Embassy in Jakarta: +62 21 3435 9000

            ## ⭐ AUTHENTIC EXPERIENCES

            1. **Unique Local Experiences:**
                * Attend a traditional Balinese cremation ceremony (if you have the opportunity).
                * Visit a local village and learn about their customs and traditions.
                * Take a yoga class with a local instructor.
                * Learn to play a traditional Balinese instrument.

            2. **Behind-the-Scenes Access:**
                * Arrange a visit to a local artist's studio.
                * Volunteer at a local community project.
                * Learn about traditional Balinese medicine from a local healer.

            3. **Meet Locals & Community Interactions:**
                * Strike up conversations with locals at markets and warungs.
                * Participate in a local festival or celebration.
                * Learn a few basic phrases in Bahasa Indonesia.

            4. **Hidden Gems:**
                * Campuhan Ridge Walk (Ubud): A scenic trail through rice fields and forests.
                * Sekumpul Waterfall (North Bali): A stunning waterfall hidden in the jungle.
                * Sidemen Valley (East Bali): A peaceful and authentic Balinese village.

            ## 🌟 A Friendly Note

            This itinerary is a great starting point, but remember that details like opening hours and prices can change. We recommend double-checking before you go! For the most up-to-date information and to customize this plan with one of our experts, please contact us on WhatsApp at +{WHATSAPP_NUMBER}. We'd love to help you create the perfect Bali journey
            """

    return f"""You are an expert travel planner specializing in authentic Bali experiences. Create a comprehensive and engaging 📅{request.duration}-days travel itinerary for 🎯{interests_text} with a budget of 💰{request.budget}.

            #Traveler Profile:#
            🎯 **Interests:** {interests_text}
            📅 **Duration:** {request.duration} days
            💰 **Budget:** {request.budget}

            **Create a Complete Itinerary That Includes:**

            🌅 **DAILY BREAKDOWN** (Provide {request.duration} full days)
            For each day, include:
            - **Morning Activity** (9-11 AM) with specific locations and times
            - **Afternoon Activity** (12-4 PM) with lunch suggestions
            - **Evening Activity** (5-8 PM) with dinner recommendations
            - **Evening Wind-down** (relaxation/beverages options)

            🏨 **HANDPICKED ACCOMMODATIONS**
            - **1** 2-3 specific hotel/villa recommendations with price ranges
            - **2** Location details and why they suit the traveler's interests
            - **3** Include both mid-range and premium options within budget

            🍜 **CULINARY EXPERIENCES**
            - **1** Daily restaurant recommendations for breakfast, lunch, dinner
            - **2** Must-try Balinese dishes with local specialties
            - **3** Food market or cooking class suggestions
            - **4** Beverage recommendations (non-alcoholic where appropriate)

            🚗 **TRANSPORTATION & LOGISTICS**
            - **1** Getting to/from airport
            - **2** Daily transportation options (cars, scooters, taxis)
            - **3** Estimated costs and booking tips

            🎭 **CULTURAL IMMERSION**
            - **1** Local customs and etiquette tips
            - **2** Temple visit protocols if applicable
            - **3** Respectful photography guidelines

            💸 **COMPREHENSIVE BUDGET BREAKDOWN**
            - **1** Daily spending estimates
            - **2** Total estimated cost vs. declared budget
            - **3** Cost-saving tips and premium upgrade options

            ⚡ **PRACTICAL INFORMATION**
            - **1** Best times to visit featured locations
            - **2** Weather considerations by season
            - **4** Emergency contacts

            ⭐ **AUTHENTIC EXPERIENCES**
            - **1** Unique local experiences not found in guidebooks
            - **2** Behind-the-scenes access opportunities
            - **3** Meet locals and community interactions
            - **4** Hidden gems based on their specific interests

            🌟 **A Friendly Note:**
            This itinerary is a great starting point, but remember that details like opening hours and prices can change. We recommend double-checking before you go! For the most up-to-date information and to customize this plan with one of our experts, please contact us on WhatsApp at +{WHATSAPP_NUMBER}. We'd love to help you create the perfect Bali journey!

            **FORMATTING REQUIREMENTS:**
            - Follow the {format_example} only for structure and headers usage
            - Use emojis liberally to make content engaging and easy to scan
            - Use markdown headers (##, ###) for clear organization with consistent spacing
            - Include bullet points and numbered lists for readability
            - Use bold text for important information
            - Ensure each day has substantial content (at least 300-400 words each)
            - Total itinerary should be comprehensive enough for the traveler to execute
            - Never use (see previous suggestions)

            Create a memorable, practical, and culturally rich Bali itinerary that exceeds expectations!
            At the end of the itinerary, ALWAYS include the A Friendly Note as is and is the same header level with other sections.
            """


def format_chat_history(history: list[ChatMessage]) -> list[dict[str, str]]:
    """Format chat history for Gemini API.

    Args: history: List of chat messages from the conversation.

    Returns: Formatted history for Gemini chat API.
    """

    formatted_history = []

    for message in history:
        # Convert role to Gemini format
        role = "user" if message.role == "user" else "model"

        content = "".join(
            part["text"] + " " for part in message.parts if "text" in part
        )
        if content.strip():
            formatted_history.append(
                {"role": role, "parts": [{"text": content.strip()}]},
            )

    return formatted_history


def create_analysis_prompt(message: str) -> str:
    """Create a prompt for contact inquiry analysis.

    Args: message: The contact message to analyze.

    Returns: A formatted prompt string for analysis.
    """

    return f"""
            Analyze the following customer inquiry for a Bali travel service company. Provide a structured analysis in JSON format.

            **Customer Message:** "{message}"

            Please analyze and provide:
            1. **Summary**: A brief 1-2 sentence summary of the inquiry
            2. **Category**: Classify into one of these categories:
            - "Booking Inquiry" - Questions about reservations, availability
            - "Itinerary Planning" - Requests for travel planning assistance
            - "General Information" - Questions about Bali, services, policies
            - "Support Request" - Issues, complaints, or assistance needed
            - "Pricing Inquiry" - Questions about costs, packages, pricing
            - "Feedback" - Reviews, testimonials, suggestions

            3. **Urgency**: Rate as "High", "Medium", or "Low"
            4. **Suggested Response**: A professional, helpful response template
            5. **Required Action**: What the team should do next
            6. **Keywords**: Key topics mentioned in the message

            Respond ONLY with a valid JSON object in this exact format:
            {{
                "summary": "Brief summary here",
                "category": "Category name",
                "urgency": "Urgency level",
                "suggested_reply": "Professional response template",
                "required_action": "Next steps for the team",
                "keywords": ["keyword1", "keyword2", "keyword3"]
            }}
            """


async def generate_itinerary(request: ItineraryRequest) -> str:
    """Generate a travel itinerary using Google Gemini AI with caching.

    Args:
        request: The itinerary request containing destination, duration, and interests.

    Returns:
        AI-generated travel itinerary.

    Raises:
        ItineraryGenerationError: If AI generation fails.

    """
    try:
        # Check cache first if enabled
        if settings.ENABLE_RESPONSE_CACHING:
            cache_key = generate_cache_key(
                "itinerary",
                {
                    "destination": request.destination,
                    "duration": request.duration,
                    "interests": sorted(request.interests),
                    "budget": request.budget,
                },
            )

            cached_result = await cache_service.get(cache_key)
            if cached_result:
                logger.info(f"Cache hit for itinerary: {request.destination}")
                return cached_result

        # Generate new itinerary
        prompt = create_itinerary_prompt(request)
        response = await ai_client.generate_content(
            prompt,
        )

        # Cache the result if enabled
        if settings.ENABLE_RESPONSE_CACHING:
            await cache_service.set(
                cache_key,
                response,
                ttl=settings.CACHE_TTL_ITINERARY,
                cache_type="itinerary",
            )
            logger.info(f"Cached itinerary for: {request.destination}")

    except Exception as e:
        logger.exception("AI itinerary generation failed")
        msg = f"Failed to generate itinerary: {e!s}"
        raise ItineraryGenerationError(msg, e) from e

    return response


async def process_query(request: QueryRequest) -> str:
    """Process user query using Google Gemini AI with conversation context and caching.

    Args:
        request: The query request containing the user's query and history.

    Returns:
        AI-generated response to the query.

    Raises:
        QueryProcessingError: If AI processing fails.

    """
    try:
        # Check cache for queries without history (common questions)
        cache_key = None
        if not request.history and settings.ENABLE_RESPONSE_CACHING:
            cache_key = generate_cache_key("query", {"query": request.query})
            cached_result = await cache_service.get(cache_key)
            if cached_result:
                logger.info(f"Cache hit for query: {request.query[:50]}...")
                return cached_result

        # Format conversation history
        history = format_chat_history(request.history)

        if history:
            # Continue existing conversation (no caching for contextual queries)
            response = await ai_client.generate_chat_response(
                request.query,
                history=history,
            )
        else:
            # Create system prompt for Bali travel assistant
            system_prompt = """ You are a knowledgeable Bali travel assistant. You help travelers with:
                            - Travel planning and itinerary suggestions
                            - Information about Bali's attractions, culture, and customs
                            - Accommodation and restaurant recommendations
                            - Transportation and logistics advice
                            - Cultural etiquette and local tips
                            - Weather and seasonal information

                            Always provide helpful, accurate, and culturally respectful information about Bali.
                            Be friendly, professional, and focus on creating memorable travel experiences. """

            # Start new conversation with system context
            full_prompt = f"{system_prompt}\n\nUser Question: {request.query}"
            response = await ai_client.generate_content(
                full_prompt,
            )

            # Cache the result for queries without history
            if cache_key and settings.ENABLE_RESPONSE_CACHING:
                await cache_service.set(
                    cache_key,
                    response,
                    ttl=settings.CACHE_TTL_QUERY,
                    cache_type="query",
                )
                logger.info(f"Cached query response: {request.query[:50]}...")

    except Exception as e:
        logger.exception("AI query processing failed")
        msg = f"Failed to process query: {e}"
        raise QueryProcessingError(msg, e) from e

    return response


async def analyze_contact(request: ContactInquiryRequest) -> dict[str, str]:
    """Analyze contact inquiry using Google Gemini AI with caching.

    Args:
        request: The contact inquiry request containing name, email, and message.

    Returns:
        AI-generated analysis of the contact inquiry.

    Raises:
        ContactAnalysisError: If AI analysis fails.

    """
    try:
        # Check cache (similar messages might have similar analysis)
        cache_key = None
        if settings.ENABLE_RESPONSE_CACHING:
            cache_key = generate_cache_key(
                "contact",
                {"message": request.message[:200]},  # Use first 200 chars for caching
            )
            cached_result = await cache_service.get(cache_key)
            if cached_result:
                logger.info("Cache hit for contact analysis")
                try:
                    return loads(cached_result)
                except JSONDecodeError:
                    logger.warning("Failed to parse cached contact analysis")

        # Generate new analysis
        prompt = create_analysis_prompt(request.message)
        response: str = await ai_client.generate_content(
            prompt,
        )

        # Parse JSON response
        try:
            analysis: dict[str, Any] = loads(response.strip())

            # Validate required fields
            required_fields = [
                "summary",
                "category",
                "urgency",
                "suggested_reply",
                "required_action",
                "keywords",
            ]
            for field in required_fields:
                if field not in analysis:
                    analysis[field] = "Not specified"

            # Convert keywords list to string if needed
            if isinstance(analysis.get("keywords"), list):
                analysis["keywords"] = ", ".join(analysis["keywords"])

            result = {
                "summary": f"{analysis['summary']}",
                "category": f"{analysis['category']}",
                "urgency": f"{analysis['urgency']}",
                "suggested_reply": f"{analysis['suggested_reply']}",
                "required_action": f"{analysis['required_action']}",
                "keywords": f"{analysis['keywords']}",
            }

            # Cache the result
            if cache_key and settings.ENABLE_RESPONSE_CACHING:
                await cache_service.set(
                    cache_key,
                    dumps(result),
                    ttl=settings.CACHE_TTL_CONTACT,
                    cache_type="contact",
                )
                logger.info("Cached contact analysis")

        except JSONDecodeError:
            # Fallback if JSON parsing fails
            logger.warning("Failed to parse AI analysis as JSON, using fallback")
            return {
                "summary": f"Analysis of inquiry from {request.name}",
                "category": "General Information",
                "urgency": "Medium",
                "suggested_reply": "Thank you for your inquiry. We'll review your message and get back to you soon.",
                "required_action": "Review and respond to customer inquiry",
                "keywords": "customer inquiry, travel, Bali",
            }

    except Exception as e:
        logger.exception("AI contact analysis failed")
        msg = f"Failed to analyze contact inquiry: {e!s}"
        raise ContactAnalysisError(msg, e) from e

    return result


async def format_itinerary_response(content: str) -> str:
    """Format the itinerary content using mdformat.

    Args: content: The raw itinerary content to format

    Returns: Formatted itinerary content as markdown string
    """
    return mdformat_text(
        content,
        options={"plugins": "mdformat-gfm", "number": 100},
    )
