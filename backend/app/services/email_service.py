"""Email service module for handling contact form email notifications.

This module contains functionality for sending email notifications
when contact form inquiries are submitted with connection pooling.
"""

from asyncio import Lock
from logging import getLogger

from fastapi_mail import ConnectionConfig, FastMail, MessageSchema, MessageType

from app.config.settings import settings
from app.schemas.models import ContactInquiryRequest
from app.services.circuit_breaker import email_circuit_breaker

logger = getLogger(__name__)


class EmailService:
    """Email service with connection pooling (singleton pattern)."""

    _instance = None
    _lock = Lock()

    def __new__(cls) -> "EmailService":
        """Create singleton instance."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        """Initialize email service."""
        if not hasattr(self, "initialized"):
            self.fm: FastMail | None = None
            self.initialized = False

    async def initialize(self) -> None:
        """Initialize FastMail connection (lazy initialization)."""
        if self.initialized:
            return

        async with self._lock:
            if self.initialized:
                return

            try:
                conf = ConnectionConfig(
                    MAIL_USERNAME=settings.MAIL_USERNAME,
                    MAIL_PASSWORD=settings.MAIL_PASSWORD,
                    MAIL_FROM=settings.MAIL_FROM,
                    MAIL_PORT=settings.MAIL_PORT,
                    MAIL_SERVER=settings.MAIL_SERVER,
                    MAIL_STARTTLS=settings.MAIL_STARTTLS,
                    MAIL_SSL_TLS=settings.MAIL_SSL_TLS,
                    USE_CREDENTIALS=True,
                    VALIDATE_CERTS=True,
                )

                self.fm = FastMail(conf)
                self.initialized = True
                logger.info("Email service initialized successfully")

            except Exception:
                logger.exception("Failed to initialize email service")
                raise

    async def send_email(
        self,
        subject: str,
        recipients: list[str],
        body: str,
        subtype: MessageType = MessageType.html,
    ) -> None:
        """Send email with circuit breaker protection.

        Args:
            subject: Email subject
            recipients: List of recipient email addresses
            body: Email body content
            subtype: Email content type (html or plain)

        Raises:
            Exception: If email sending fails

        """
        # Ensure service is initialized
        await self.initialize()

        async def _send() -> None:
            if not self.fm:
                logger.error("Email service not initialized before sending email.")
                raise RuntimeError("Email service not initialized.")

            try:
                message = MessageSchema(
                    subject=subject,
                    recipients=recipients,
                    body=body,
                    subtype=subtype,
                )
                await self.fm.send_message(message)
            except Exception as e:
                logger.exception(f"Failed to send email: {subject}")
                raise RuntimeError(f"Email sending failed: {e}") from e

        # Execute with circuit breaker protection
        try:
            await email_circuit_breaker.call(_send)
            logger.info(f"Email sent successfully: {subject}")
        except Exception:
            logger.exception("Failed to send email")
            raise


# Global email service instance
email_service = EmailService()


async def send_contact_inquiry_email(
    request: ContactInquiryRequest,
    analysis_result: dict[str, str],
) -> None:
    """Send email notification for contact inquiry.

    Args:
        request: The contact inquiry request with name, email, and message
        analysis_result: The AI analysis result of the inquiry

    Raises:
        Exception: If email sending fails

    """
    html = f"""
    <div style="font-family: sans-serif; line-height: 1.6;">
      <h2>New Contact Inquiry from BaliBlissed Website</h2>
      <p><strong>Name:</strong> {request.name}</p>
      <p><strong>Email:</strong> <a href="mailto:{request.email}">{request.email}</a></p>
      <hr>
      <h3>Message:</h3>
      <p style="white-space: pre-wrap;">{request.message}</p>
      <hr>
      <h3>AI Analysis:</h3>
      <ul>
        <li><strong>Summary:</strong> {analysis_result.get("summary", "N/A")}</li>
        <li><strong>Category:</strong> {analysis_result.get("category", "N/A")}</li>
        <li><strong>Urgency:</strong> {analysis_result.get("urgency", "N/A")}</li>
        <li><strong>Keywords:</strong> {analysis_result.get("keywords", "N/A")}</li>
      </ul>
    </div>
    """

    await email_service.send_email(
        subject=f"New Contact Inquiry from {request.name}",
        recipients=[settings.MAIL_FROM],
        body=html,
        subtype=MessageType.html,
    )
    logger.info(f"Successfully sent contact inquiry email from {request.name}")
