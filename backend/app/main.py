"""Main application module for the BaliBlissed backend.

This module creates the FastAPI application instance and defines all
API endpoints, including error handlers and health checks with performance optimizations.
"""

from datetime import datetime
from logging import getLogger
from typing import Any

from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException, Request, status
from fastapi.responses import JSONResponse

from app.config.settings import settings
from app.exceptions.ai_exceptions import (
    AIGenerationError,
    ContactAnalysisError,
    ItineraryGenerationError,
    QueryProcessingError,
)
from app.middleware.middleware import (
    add_compression,
    add_request_logging,
    add_security_headers,
    configure_cors,
    lifespan,
)
from app.schemas.models import (
    ContactAnalysisResponse,
    ContactInquiryRequest,
    ErrorResponse,
    ItineraryRequest,
    ItineraryResponse,
    QueryRequest,
    QueryResponse,
)
from app.services.ai_client import ai_client
from app.services.ai_service import (
    analyze_contact,
    create_error_response,
    format_itinerary_response,
    generate_itinerary,
    process_query,
)
from app.services.circuit_breaker import (
    CircuitBreakerError,
    ai_circuit_breaker,
    email_circuit_breaker,
)
from app.services.email_service import send_contact_inquiry_email
from app.services.metrics import get_system_metrics, metrics_collector
from app.services.rate_limiter import rate_limit_middleware

# --- Application Initialization ---
app = FastAPI(
    title="BaliBlissed AI Backend",
    description="Provides AI-powered services for the BaliBlissed Next.js application.",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT != "production" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT != "production" else None,
    lifespan=lifespan,
)


# --- Middleware Configuration ---
add_security_headers(app)
add_request_logging(app)
configure_cors(app)
add_compression(app)


# --- API Endpoints ---


@app.get("/", response_model=dict[str, str])
async def read_root() -> dict[str, str]:
    """Health check endpoint to verify server status."""
    return {"status": "ok", "message": "Welcome to the BaliBlissed AI Backend!"}


@app.post(
    "/api/suggest-itinerary",
    response_model=ItineraryResponse,
    status_code=status.HTTP_200_OK,
    summary="Generate Travel Itinerary",
    description="Generate a personalized travel itinerary based on destination, duration, and interests.",
    responses={
        400: {"model": ErrorResponse, "description": "Invalid request data"},
        429: {"model": ErrorResponse, "description": "Rate limit exceeded"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
        503: {"model": ErrorResponse, "description": "Service temporarily unavailable"},
    },
)
async def suggest_itinerary(
    request: ItineraryRequest,
    http_request: Request,
) -> ItineraryResponse:
    """Generate a travel itinerary based on user preferences.

    This endpoint processes travel preferences and generates a customized itinerary
    using Google Gemini AI, specifically tailored for Bali travel experiences.

    Args:
        request: ItineraryRequest containing destination, duration, and interests
        http_request: FastAPI request object for rate limiting

    Returns:
        ItineraryResponse: Generated travel itinerary

    Raises:
        ValueError: If input validation fails
        HTTPException: 400 for invalid input, 429 for rate limit, 500 for server errors

    """
    logger = getLogger(__name__)

    # Apply rate limiting (10 requests per hour for itinerary generation)
    await rate_limit_middleware(http_request, max_requests=10, window_seconds=3600)

    logger.info(
        f"Processing itinerary request for {request.destination} "
        f"({request.duration} days, {len(request.interests)} interests)",
    )

    try:
        # Generate itinerary using AI
        itinerary_content = await generate_itinerary(request)

        logger.info(f"Successfully generated itinerary for {request.destination}")
        return ItineraryResponse(
            itinerary=await format_itinerary_response(itinerary_content),
        )

    except ValueError as e:
        logger.warning(f"Invalid input for itinerary generation: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except CircuitBreakerError as e:
        logger.exception("Circuit breaker open for itinerary generation")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="AI service temporarily unavailable. Please try again later.",
        ) from e
    except TimeoutError as e:
        logger.exception("Timeout in itinerary generation")
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Request timed out. Please try again.",
        ) from e
    except ItineraryGenerationError as e:
        mssg = f"Unexpected error in itinerary generation: {e!s}"
        logger.exception(mssg)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate itinerary.",
        ) from e


@app.post(
    "/api/answer-query",
    response_model=QueryResponse,
    status_code=status.HTTP_200_OK,
    summary="Answer User Query",
    description="Process user queries with conversation history context.",
    responses={
        400: {"model": ErrorResponse, "description": "Invalid request data"},
        429: {"model": ErrorResponse, "description": "Rate limit exceeded"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
        503: {"model": ErrorResponse, "description": "Service temporarily unavailable"},
    },
)
async def answer_query(
    request: QueryRequest,
    http_request: Request,
) -> QueryResponse:
    """Answer a user's query with conversation history context.

    This endpoint processes user queries and maintains conversation context
    through chat history using Google Gemini AI as a Bali travel assistant.

    Args:
        request: QueryRequest containing the user's query and chat history
        http_request: FastAPI request object for rate limiting

    Returns:
        QueryResponse: AI-generated response to the query

    Raises:
        ValueError: If input validation fails
        HTTPException: 400 for invalid input, 429 for rate limit, 500 for server errors

    """
    logger = getLogger(__name__)

    # Apply rate limiting (30 requests per hour for chatbot)
    await rate_limit_middleware(http_request, max_requests=30, window_seconds=3600)

    logger.info(
        f"Processing query (length: {len(request.query)}, "
        f"history: {len(request.history)} messages)",
    )

    try:
        # Process query using AI
        answer_content = await process_query(request)

        logger.info("Successfully processed user query")
        return QueryResponse(answer=answer_content)

    except ValueError as e:
        logger.warning(f"Invalid input for query processing: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except CircuitBreakerError as e:
        logger.exception("Circuit breaker open for query processing")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="AI service temporarily unavailable. Please try again later.",
        ) from e
    except TimeoutError as e:
        logger.exception("Timeout in query processing")
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Request timed out. Please try again.",
        ) from e
    except QueryProcessingError as e:
        mssg = f"Unexpected error in query processing: {e!s}"
        logger.exception(mssg)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process query.",
        ) from e


@app.post(
    "/api/handle-contact-inquiry",
    response_model=ContactAnalysisResponse,
    status_code=status.HTTP_200_OK,
    summary="Analyze Contact Inquiry",
    description="Analyze and categorize contact form submissions.",
    responses={
        400: {"model": ErrorResponse, "description": "Invalid request data"},
        429: {"model": ErrorResponse, "description": "Rate limit exceeded"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
        503: {"model": ErrorResponse, "description": "Service temporarily unavailable"},
    },
)
async def handle_contact_inquiry(
    request: ContactInquiryRequest,
    http_request: Request,
    background_tasks: BackgroundTasks,
) -> ContactAnalysisResponse:
    """Analyze a contact inquiry to categorize it and provide insights.

    This endpoint processes contact form submissions and provides analysis
    including categorization and suggested responses. Email sending is done
    in the background to improve response time.

    Args:
        request: ContactInquiryRequest containing name, email, and message
        http_request: FastAPI request object for rate limiting
        background_tasks: FastAPI background tasks for async email sending

    Returns:
        ContactAnalysisResponse: Analysis of the contact inquiry

    Raises:
        ValueError: If input validation fails
        HTTPException: 400 for invalid input, 429 for rate limit, 500 for server errors

    """
    logger = getLogger(__name__)

    # Apply rate limiting (more lenient for development)
    max_requests = 50 if settings.ENVIRONMENT == "development" else 5
    await rate_limit_middleware(
        http_request, max_requests=max_requests, window_seconds=3600
    )

    logger.info(
        f"Processing contact inquiry from {request.name} "
        f"(message length: {len(request.message)})",
    )

    try:
        # Analyze contact inquiry using AI
        analysis_result = await analyze_contact(request)
        logger.info(f"Successfully analyzed contact inquiry from {request.name}")

        # Send email notification in background (non-blocking)
        background_tasks.add_task(send_contact_inquiry_email, request, analysis_result)

        return ContactAnalysisResponse(
            confirmation=f"Thank you, {request.name}! We have received your message and will get back to you shortly.",
        )

    except ValueError as e:
        logger.warning(f"Invalid input for contact inquiry analysis: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
    except CircuitBreakerError as e:
        logger.exception("Circuit breaker open for contact inquiry analysis")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="AI service temporarily unavailable. Please try again later.",
        ) from e
    except TimeoutError as e:
        logger.exception("Timeout in contact inquiry analysis")
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Request timed out. Please try again.",
        ) from e
    except ContactAnalysisError as e:
        mssg = f"Unexpected error in contact inquiry analysis: {e!s}"
        logger.exception(mssg)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process contact inquiry.",
        ) from e


# --- Error Handling Middleware ---


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions with structured error responses.

    Args: request: The incoming request that caused the HTTP exception,
        exc: The HTTPException that was raised.

    Returns:  A JSONResponse with HTTP error details.
    """

    logger = getLogger(__name__)

    logger.exception(
        f"HTTP {exc.status_code} error for {request.method} {request.url.path}: {exc.detail}",
    )

    error_response: dict[str, Any] = create_error_response(
        message=f"HTTP {exc.status_code} Error",
        detail=exc.detail,
    )

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response,
    )


@app.exception_handler(ValueError)
async def validation_exception_handler(
    request: Request,
    exc: ValueError,
) -> JSONResponse:
    """Handle validation errors with detailed error messages.

    Args: request: The incoming request that caused the validation error.
        exc: The ValueError that was raised.

    Returns: A JSONResponse with validation error details.
    """

    logger = getLogger(__name__)

    logger.exception(f"Validation error for {request.url.path}: {exc}")

    error_response: dict[str, Any] = create_error_response(
        message="Validation Error",
        detail=str(exc),
    )

    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=error_response,
    )


@app.exception_handler(AIGenerationError)
async def ai_generation_exception_handler(
    request: Request,
    exc: AIGenerationError,
) -> JSONResponse:
    """Handle AI generation errors with structured error responses.

    Args: request: The incoming request that caused the exception,
        exc: The AIGenerationError that was raised.

    Returns: A JSONResponse with AI generation error details.
    """

    logger = getLogger(__name__)

    logger.exception(
        f"AI generation error for {request.method} {request.url.path}: {exc}",
    )

    error_response: dict[str, Any] = create_error_response(
        message="AI Generation Error",
        detail=str(exc),
    )

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response,
    )


@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle unexpected exceptions with generic error response.

    Args: request: The incoming request that caused the exception.
        exc: The exception that was raised.

    Returns: A JSONResponse with error details.
    """

    logger = getLogger(__name__)

    logger.exception(
        f"Unhandled exception for {request.method} {request.url.path}: {exc}",
    )

    error_response: dict[str, Any] = create_error_response(
        message="Internal Server Error",
        detail="An unexpected server error occurred.",
    )

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response,
    )


# --- Health Check Endpoints ---


@app.get("/health", response_model=dict[str, Any])
async def health_check() -> dict[str, Any]:
    """Detailed health check endpoint for monitoring.

    Returns:
        A dictionary containing health status information.

    """

    return {
        "status": "healthy",
        "timestamp": datetime.now(datetime.now().astimezone().tzinfo).isoformat(),
        "version": "1.0.0",
        "services": {
            "ai_client": "initialized" if ai_client.initialized else "not_initialized",
            "ai_circuit_breaker": ai_circuit_breaker.get_state(),
            "email_circuit_breaker": email_circuit_breaker.get_state(),
        },
    }


@app.get("/ready", response_model=dict[str, str])
async def readiness_check() -> dict[str, str]:
    """Readiness check for container orchestration.

    Returns:
        A dictionary containing readiness status information.

    """

    # Check if AI client is initialized
    if not ai_client.initialized:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="AI client not initialized",
        )

    return {
        "status": "ready",
        "timestamp": datetime.now(datetime.now().astimezone().tzinfo).isoformat(),
    }


@app.get("/metrics", response_model=dict[str, Any])
async def get_metrics() -> dict[str, Any]:
    """Get API performance metrics.

    Returns:
        Dictionary containing performance metrics

    """

    api_metrics = metrics_collector.get_metrics()
    system_metrics = await get_system_metrics()

    return {
        "timestamp": datetime.now(datetime.now().astimezone().tzinfo).isoformat(),
        "api_metrics": api_metrics,
        "system_metrics": system_metrics,
    }
