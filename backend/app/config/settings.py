"""Application settings and configuration constants.

This module contains application settings, constants, and configuration
values for the BaliBlissed backend application.
"""

from pathlib import Path

from pydantic import EmailStr
from pydantic_settings import BaseSettings, SettingsConfigDict

ENV_FILE = Path(__file__).parent.parent.parent / ".env"

# --- Constants ---
MAX_QUERY_LENGTH = 1000
MAX_MESSAGE_LENGTH = 2000
MAX_DESTINATION_LENGTH = 100
MAX_NAME_LENGTH = 100
MIN_TRIP_DURATION = 1
MAX_TRIP_DURATION = 365
MAX_INTERESTS_COUNT = 20
MIN_MESSAGE_LENGTH = 10

WHATSAPP_NUMBER = "+6285847006743"

# Rate limiting constants
RATE_LIMIT_REQUESTS = 100
RATE_LIMIT_WINDOW = 3600  # 1 hour in seconds

# Response constants
DEFAULT_ERROR_MESSAGE = "An unexpected server error occurred."
ITINERARY_GENERATION_ERROR = "Failed to generate itinerary."
QUERY_PROCESSING_ERROR = "Failed to process query."
CONTACT_INQUIRY_ERROR = "Failed to process contact inquiry."

# AI Model Configuration
GEMINI_MODEL = "gemini-2.0-flash"
GENERATION_CONFIG = {
    "temperature": 0.7,
    "top_p": 0.8,
    "top_k": 40,
    "max_output_tokens": 4096 * 2,
}

# Safety settings for content generation
SAFETY_SETTINGS = [
    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {
        "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        "threshold": "BLOCK_MEDIUM_AND_ABOVE",
    },
    {
        "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
        "threshold": "BLOCK_MEDIUM_AND_ABOVE",
    },
]


class Settings(BaseSettings):
    """Application settings with validation and default values."""

    model_config = SettingsConfigDict(
        env_file=f"{ENV_FILE}",
        env_file_encoding="utf-8",
        extra="ignore",
    )

    # AI Configuration
    GEMINI_API_KEY: str
    AI_REQUEST_TIMEOUT: int = 60  # seconds
    AI_MAX_RETRIES: int = 2

    # Environment
    ENVIRONMENT: str = "development"
    LOG_TO_FILE: bool = False
    PRODUCTION_FRONTEND_URL: str | None = None

    # Email Configuration
    MAIL_USERNAME: str
    MAIL_PASSWORD: str
    MAIL_FROM: EmailStr
    MAIL_PORT: int
    MAIL_SERVER: str
    MAIL_STARTTLS: bool
    MAIL_SSL_TLS: bool

    # Redis Configuration (optional)
    REDIS_ENABLED: bool = False
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: str | None = None

    # Performance Configuration
    MAX_CONCURRENT_AI_REQUESTS: int = 10
    ENABLE_RESPONSE_CACHING: bool = True
    CACHE_TTL_ITINERARY: int = 86400  # 24 hours
    CACHE_TTL_QUERY: int = 3600  # 1 hour
    CACHE_TTL_CONTACT: int = 1800  # 30 minutes


# Initialize settings from environment variables
try:
    settings = Settings()
    # Validate critical settings
    if not settings.GEMINI_API_KEY:
        raise ValueError("GEMINI_API_KEY is required but not set")
    if not settings.MAIL_USERNAME or not settings.MAIL_PASSWORD:
        raise ValueError(
            "Email configuration (MAIL_USERNAME, MAIL_PASSWORD) is required"
        )
except Exception as e:
    print(f"Configuration error: {e}")
    raise
