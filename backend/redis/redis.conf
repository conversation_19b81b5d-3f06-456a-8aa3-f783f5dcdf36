# redis/redis.conf
# Redis configuration for BaliBlissed backend
# Optimized for both development and production environments

# =============================================================================
# NETWORK AND SECURITY
# =============================================================================

# Bind to all interfaces (Docker networking)
bind 0.0.0.0

# Default port
port 6379

# Disable protected mode for Docker (handled by network isolation)
protected-mode no

# Connection timeout (0 = disable)
timeout 300

# TCP keepalive
tcp-keepalive 300

# =============================================================================
# GENERAL CONFIGURATION
# =============================================================================

# Run as daemon (handled by Docker)
daemonize no

# Process ID file (not needed in Docker)
# pidfile /var/run/redis.pid

# Log level: debug, verbose, notice, warning
loglevel notice

# Log to stdout (Docker will capture)
logfile ""

# Number of databases
databases 16

# =============================================================================
# PERSISTENCE CONFIGURATION
# =============================================================================

# RDB Snapshots
# Save the DB on disk:
# save <seconds> <changes>
# Save if at least 1 key changed in 900 seconds
save 900 1
# Save if at least 10 keys changed in 300 seconds
save 300 10
# Save if at least 10000 keys changed in 60 seconds
save 60 10000

# Compress RDB files
rdbcompression yes

# Checksum RDB files
rdbchecksum yes

# RDB filename
dbfilename dump.rdb

# Working directory
dir /data

# AOF (Append Only File) persistence
appendonly yes
appendfilename "appendonly.aof"

# AOF sync policy: always, everysec, no
appendfsync everysec

# Rewrite AOF when it grows by this percentage
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# =============================================================================
# MEMORY MANAGEMENT
# =============================================================================

# Maximum memory (0 = unlimited, set via Docker limits)
maxmemory 0

# Memory eviction policy when maxmemory is reached
# allkeys-lru: Remove any key according to LRU algorithm
# volatile-lru: Remove keys with expire set according to LRU
# allkeys-random: Remove random keys
# volatile-random: Remove random keys with expire set
# volatile-ttl: Remove keys with expire set and shorter TTL
# noeviction: Don't evict, return error on write
maxmemory-policy allkeys-lru

# Sample size for LRU/TTL algorithms
maxmemory-samples 5

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Disable slow log for development (enable in production)
slowlog-log-slower-than 10000
slowlog-max-len 128

# Hash table rehashing
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List compression
list-max-ziplist-size -2
list-compress-depth 0

# Set optimization
set-max-intset-entries 512

# Sorted set optimization
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog optimization
hll-sparse-max-bytes 3000

# =============================================================================
# CLIENT MANAGEMENT
# =============================================================================

# Maximum number of connected clients
maxclients 10000

# Client timeout (0 = disable)
timeout 0

# Client output buffer limits
# client-output-buffer-limit <class> <hard limit> <soft limit> <soft seconds>
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# =============================================================================
# SECURITY (Production)
# =============================================================================

# Require password (set via environment variable)
# requirepass ${REDIS_PASSWORD}

# Disable dangerous commands in production
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command KEYS ""
# rename-command CONFIG ""
# rename-command SHUTDOWN SHUTDOWN_BALIBLISSED
# rename-command DEBUG ""
# rename-command EVAL ""

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable keyspace notifications for development
notify-keyspace-events "Ex"

# Disable RDB saves on BGSAVE errors (development only)
stop-writes-on-bgsave-error no

# =============================================================================
# MONITORING AND STATS
# =============================================================================

# Enable latency monitoring
latency-monitor-threshold 100

# =============================================================================
# MODULES AND EXTENSIONS
# =============================================================================

# Load modules (if needed)
# loadmodule /path/to/module.so

# =============================================================================
# CLUSTER CONFIGURATION (Future)
# =============================================================================

# Uncomment for Redis Cluster setup
# cluster-enabled yes
# cluster-config-file nodes.conf
# cluster-node-timeout 15000
# cluster-require-full-coverage yes
