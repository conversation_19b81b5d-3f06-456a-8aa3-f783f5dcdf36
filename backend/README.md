# BaliBlissed Backend

A high-performance FastAPI backend for the BaliBlissed travel platform, featuring AI-powered itinerary generation, Redis caching, and comprehensive monitoring.

## 🚀 Features

- **AI-Powered Travel Services**: Intelligent itinerary generation and query processing using Google Gemini
- **High-Performance Caching**: Redis-based caching with configurable TTL for optimal response times
- **Robust Architecture**: Circuit breakers, rate limiting, and comprehensive error handling
- **Email Integration**: FastAPI-Mail for contact inquiries and notifications
- **Performance Monitoring**: Built-in metrics, logging, and health checks
- **Docker-Ready**: Complete containerization with development and production configurations
- **Auto-Reload Development**: Hot reload for efficient development workflow

## 🏗️ Architecture

```text
backend/
├── app/                    # Main application code
│   ├── config/            # Configuration and settings
│   ├── exceptions/        # Custom exception handlers
│   ├── middleware/        # CORS, security, logging middleware
│   ├── models/           # Data models and schemas
│   ├── schemas/          # Pydantic models for API
│   └── services/         # Business logic and external integrations
├── docs/                 # Documentation
├── redis/               # Redis configuration
├── scripts/             # Docker management scripts
├── docker-compose.yml   # Multi-service orchestration
└── Dockerfile          # Multi-stage container build
```

## 🛠️ Tech Stack

- **Framework**: FastAPI 0.116.2+ with Python 3.13
- **AI Integration**: Google Generative AI (Gemini)
- **Caching**: Redis 7-alpine with persistence
- **Email**: FastAPI-Mail with SMTP support
- **Containerization**: Docker with multi-stage builds
- **Package Management**: UV for fast dependency resolution
- **Code Quality**: Ruff for linting and formatting

## 📋 API Endpoints

### Core Services

- `GET /health` - Health check with system metrics
- `POST /api/query` - Process travel queries with AI
- `POST /api/itinerary` - Generate detailed travel itineraries
- `POST /api/contact` - Handle contact form submissions

### Monitoring

- `GET /metrics` - Application performance metrics
- `GET /docs` - Interactive API documentation (Swagger UI)
- `GET /redoc` - Alternative API documentation

## 🚀 Quick Start

### Prerequisites

- Docker Desktop
- Node.js (for npm scripts)

### Development Setup

1. **Clone and navigate to backend**:

   ```bash
   cd backend
   ```

2. **Start development environment**:

   ```bash
   npm run docker:start
   ```

3. **Access services**:

   - Backend API: <http://localhost:8000>
   - API Documentation: <http://localhost:8000/docs>
   - Redis Commander: <http://localhost:8081>
   - Health Check: <http://localhost:8000/health>

4. **View logs**:

   ```bash
   npm run docker:logs
   ```

5. **Stop services**:

   ```bash
   npm run docker:stop
   ```

## 🔧 Configuration

### Environment Variables

Create `.env` file in project root:

```env
# AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here
AI_MODEL=gemini-1.5-flash
MAX_CONCURRENT_AI_REQUESTS=10

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
ENABLE_RESPONSE_CACHING=true

# Cache TTL (seconds)
CACHE_TTL_ITINERARY=86400    # 24 hours
CACHE_TTL_QUERY=3600         # 1 hour
CACHE_TTL_CONTACT=1800       # 30 minutes

# Email Configuration
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_FROM=<EMAIL>
MAIL_PORT=587
MAIL_SERVER=smtp.gmail.com
MAIL_STARTTLS=true
MAIL_SSL_TLS=false

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Application
DEBUG=false
LOG_LEVEL=INFO
```

### Docker Services

The backend runs with these services:

- **Backend**: FastAPI application with auto-reload
- **Redis**: Caching and session storage
- **Redis Commander**: Web-based Redis management (development only)

## 📚 Documentation

Comprehensive documentation is available in the `docs/` directory:

- **[Quick Start Guide](docs/QUICK_START.md)** - Get up and running quickly
- **[Docker Development Guide](docs/DOCKER_DEVELOPMENT_GUIDE.md)** - Adding packages, databases, services
- **[Docker Redis Setup Summary](docs/DOCKER_REDIS_SETUP_SUMMARY.md)** - Redis configuration details
- **[Performance Analysis](docs/PERFORMANCE_ANALYSIS_REPORT.md)** - Performance metrics and optimizations
- **[Performance Improvements](docs/PERFORMANCE_IMPROVEMENTS.md)** - Optimization strategies
- **[Deliverables Summary](docs/DELIVERABLES_SUMMARY.md)** - Project deliverables overview

## 🔄 Development Workflow

### Auto-Reload Development

The Docker setup includes automatic code reloading:

- Volume mount: Source code is mounted into the container
- Uvicorn `--reload`: Automatically restarts on file changes
- No rebuild required: Just edit Python files and see changes instantly

### Adding Dependencies

1. Add package to `pyproject.toml`
2. Restart containers: `npm run docker:stop && npm run docker:start`

### Database Integration

See [Docker Development Guide](docs/DOCKER_DEVELOPMENT_GUIDE.md) for adding PostgreSQL, MySQL, or MongoDB.

## 🧪 Testing

### Load Testing

```bash
# Run load tests
python load_test_example.py
```

### Health Checks

```bash
# Check application health
curl http://localhost:8000/health

# Check Redis connectivity
curl http://localhost:8000/health | jq '.redis'
```

## 📊 Performance Features

- **Circuit Breakers**: Prevent cascade failures in AI and email services
- **Rate Limiting**: Configurable request throttling
- **Response Caching**: Redis-based caching with intelligent TTL
- **Connection Pooling**: Optimized database and Redis connections
- **Compression**: Gzip compression for API responses
- **Monitoring**: Built-in metrics and health checks

## 🔒 Security

- **CORS Configuration**: Configurable cross-origin resource sharing
- **Security Headers**: Comprehensive security header middleware
- **Input Validation**: Pydantic-based request validation
- **Error Handling**: Secure error responses without sensitive data exposure

## 🐳 Docker Commands

```bash
# Development
npm run docker:start     # Start all services
npm run docker:stop      # Stop all services
npm run docker:restart   # Restart services
npm run docker:status    # Check service status
npm run docker:logs      # View logs

# Individual services
npm run docker:logs backend        # Backend logs only
npm run docker:logs redis          # Redis logs only
npm run docker:logs redis-commander # Redis Commander logs
```

## 🚀 Production Deployment

For production deployment, use the production Docker Compose profile:

```bash
cd backend
./scripts/docker-prod.sh deploy
```

See production scripts in `scripts/docker-prod.sh` for advanced deployment options.

## 🤝 Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation
4. Use the provided linting configuration (Ruff)
5. Test with Docker before submitting

## 📝 License

This project is part of the BaliBlissed travel platform.

---

For detailed setup instructions and troubleshooting, see the [Quick Start Guide](docs/QUICK_START.md).
