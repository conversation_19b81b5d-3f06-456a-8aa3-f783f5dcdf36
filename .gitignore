# macOS
**/.DS_Store*
**/Icon?
._*
!**/icons/
!**/*Icon.tsx
.Spotlight-V100
.Trashes
.AppleDouble
.LSOverride
*.pem

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

.genkit/

data/redis/

# Linux
*~
.nfs*

# Next.js
/.next/
/out/

# Editors/IDEs
# Keep recommended project settings in .vscode while ignoring local machine files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json

# IDE
.idea/
*.iml
*.swp
*.swo
*.swn
.history/

*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
bun-debug.log*



# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/
bower_components/
/node_modules
/.pnp
.pnp.js


# Optional npm cache directory
.npm

# npm pack output
*.tgz

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.*
!.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
out/

# Temporary folders
tmp/
temp/

# Live Server
.live-server/

# Caches
.cache/
.parcel-cache/
.eslintcache
.stylelintcache
.turbo/
.vite/
.vitest/
node_modules/.vite/
*.tsbuildinfo


# Framework/build specific (harmless if unused)
.next/
.nuxt/
.svelte-kit/
.storybook-out/   # older patterns
storybook-static/

# Dev servers
.live-server/

# Temporary folders
tmp/
temp/

# Test artifacts
# Cypress
cypress/screenshots/
cypress/videos/
cypress/downloads/

# Playwright
playwright-report/
test-results/
playwright/.cache/


# Puppeteer
.pptr/
puppeteer/.local-chromium/

# Deployment/platform
.vercel/
.netlify/
.firebase/

# Backup files
**/*.bak
**/*.bkp
**/*.tmp
**/*.old
**/*.orig
**/*.rej
**/*.swo
**/*.swp
**/*.swn
**/*.~*
**/*~


## project specific

# # Images
# **/*.[jJ][pP][gG]
# **/*.[jJ][pP][eE][gG]
# **/*.[pP][nN][gG]
# **/*.[gG][iI][fF]
# **/*.[sS][vV][gG]
# **/*.[wW][eE][bB][pP]
# **/*.[aA][vV][iI][fF]
# **/*.[iI][cC][oO]
# **/*.[bB][mM][pP]

# /public/images/
# /public/icons/

# Project-specific: Python code under /python only
/python/**/__pycache__/
/python/**/*.pyc
/python/**/*.pyo
/python/**/*.pyd
/python/**/*.egg-info/
/python/*.egg
/python/*.pyw
/python/*.pyi
/python/.venv/

# Local environment/automation
.direnv/
.qodo
