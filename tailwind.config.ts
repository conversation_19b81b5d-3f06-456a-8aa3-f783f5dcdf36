import type { Config } from "tailwindcss";
import tailwindcssAnimate from "tailwindcss-animate";
import tailwindcssTypography from "@tailwindcss/typography";

const config = {
    darkMode: "selector",
    content: [
        "./pages/**/*.{ts,tsx}",
        "./components/**/*.{ts,tsx}",
        "./app/**/*.{ts,tsx}",
        "./src/**/*.{ts,tsx}",
    ],
    prefix: "",
    theme: {
        container: {
            center: true,
            padding: "2rem",
            screens: {
                "2xl": "1400px",
            },
        },
        extend: {
            fontFamily: {
                body: ["var(--font-pt-sans)", "sans-serif"],
                headline: ["var(--font-pt-sans)", "sans-serif"],
                code: ["monospace"],
                balibanat: ["var(--font-bali-banat)"],
                mangusastra: ["var(--font-mangusastra)"],
            },
            colors: {
                border: "var(--border)",
                input: "var(--input)",
                ring: "var(--ring)",
                background: "var(--background)",
                foreground: "var(--foreground)",
                primary: {
                    DEFAULT: "var(--primary)",
                    foreground: "var(--primary-foreground)",
                },
                secondary: {
                    DEFAULT: "var(--secondary)",
                    foreground: "var(--secondary-foreground)",
                },
                destructive: {
                    DEFAULT: "var(--destructive)",
                    foreground: "var(--destructive-foreground)",
                },
                muted: {
                    DEFAULT: "var(--muted)",
                    foreground: "var(--muted-foreground)",
                },
                accent: {
                    DEFAULT: "var(--accent)",
                    foreground: "var(--accent-foreground)",
                },
                popover: {
                    DEFAULT: "var(--popover)",
                    foreground: "var(--popover-foreground)",
                },
                card: {
                    DEFAULT: "var(--card)",
                    foreground: "var(--card-foreground)",
                },
                chart: {
                    "1": "var(--chart-1)",
                    "2": "var(--chart-2)",
                    "3": "var(--chart-3)",
                    "4": "var(--chart-4)",
                    "5": "var(--chart-5)",
                },
                sidebar: {
                    DEFAULT: "var(--sidebar-background)",
                    foreground: "var(--sidebar-foreground)",
                    primary: "var(--sidebar-primary)",
                    "primary-foreground": "var(--sidebar-primary-foreground)",
                    accent: "var(--sidebar-accent)",
                    "accent-foreground": "var(--sidebar-accent-foreground)",
                    border: "var(--sidebar-border)",
                    ring: "var(--sidebar-ring)",
                },
            },
            borderRadius: {
                lg: "var(--radius)",
                md: "calc(var(--radius) - 2px)",
                sm: "calc(var(--radius) - 4px)",
            },
            keyframes: {
                "accordion-down": {
                    from: { height: "0" },
                    to: { height: "var(--radix-accordion-content-height)" },
                },
                "accordion-up": {
                    from: { height: "var(--radix-accordion-content-height)" },
                    to: { height: "0" },
                },
            },
            animation: {
                "accordion-down": "accordion-down 0.2s ease-out",
                "accordion-up": "accordion-up 0.2s ease-out",
            },
        },
        typography: ({ theme }) => ({
            DEFAULT: {
                css: {
                    // Headings
                    h1: {
                        color: theme("colors.gray.900"),
                        fontWeight: "700",
                        fontSize: theme("fontSize.4xl")[0],
                        borderBottom: `1px solid ${theme("colors.gray.200")}`,
                        paddingBottom: theme("spacing.2"),
                        marginTop: theme("spacing.10"),
                        marginBottom: theme("spacing.6"),
                    },
                    h2: {
                        color: theme("colors.gray.900"),
                        fontWeight: "700",
                        fontSize: theme("fontSize.2xl")[0],
                        borderBottom: `1px solid ${theme("colors.gray.100")}`,
                        paddingBottom: theme("spacing.1"),
                        marginTop: theme("spacing.8"),
                        marginBottom: theme("spacing.4"),
                    },
                    h3: {
                        color: theme("colors.gray.900"),
                        fontWeight: "600",
                        fontSize: theme("fontSize.xl")[0],
                        marginTop: theme("spacing.6"),
                        marginBottom: theme("spacing.9"),
                    },
                    // Lists
                    ol: {
                        listStyleType: "decimal",
                        listStylePosition: "inside",
                        color: theme("colors.gray.800"),
                        paddingLeft: theme("spacing.5"),
                        marginBottom: theme("spacing.4"),
                    },
                    ul: {
                        listStyleType: "disc",
                        listStylePosition: "inside",
                        color: theme("colors.gray.800"),
                        paddingLeft: theme("spacing.5"),
                        marginBottom: theme("spacing.4"),
                    },
                    li: {
                        marginBottom: "0", // Prevents extra spacing in lists
                        paddingLeft: "0",
                    },
                    // Inline strong fix
                    strong: {
                        color: theme("colors.gray.900"),
                        fontWeight: "600",
                        display: "inline", // Fixes the inline issue in lists
                    },
                    // Blockquotes
                    blockquote: {
                        borderLeftColor: theme("colors.blue.300"),
                        color: theme("colors.blue.800"),
                        backgroundColor: theme("colors.blue.50"),
                        fontStyle: "italic",
                        borderLeftWidth: "4px",
                        borderRadius: theme("borderRadius.lg"),
                        paddingLeft: theme("spacing.4"),
                        paddingTop: theme("spacing.2"),
                        paddingBottom: theme("spacing.2"),
                        marginBottom: theme("spacing.4"),
                    },
                    // Code
                    code: {
                        backgroundColor: theme("colors.gray.100"),
                        color: theme("colors.pink.600"),
                        borderRadius: theme("borderRadius.sm"),
                        paddingLeft: theme("spacing.1.5"),
                        paddingRight: theme("spacing.1.5"),
                        paddingTop: "2px",
                        paddingBottom: "2px",
                        fontSize: theme("fontSize.sm")[0],
                    },
                    "pre code": {
                        backgroundColor: "transparent",
                        color: "inherit",
                        padding: "0",
                    },
                    pre: {
                        backgroundColor: theme("colors.gray.900"),
                        color: theme("colors.gray.100"),
                        borderRadius: theme("borderRadius.lg"),
                        padding: theme("spacing.4"),
                        fontSize: theme("fontSize.sm")[0],
                        overflowX: "auto",
                        marginTop: theme("spacing.4"),
                        marginBottom: theme("spacing.4"),
                    },
                    // Tables
                    table: {
                        width: "100%",
                        borderCollapse: "collapse",
                        fontSize: theme("fontSize.sm")[0],
                        marginBottom: theme("spacing.4"),
                    },
                    th: {
                        backgroundColor: theme("colors.gray.100"),
                        color: theme("colors.gray.900"),
                        fontWeight: "600",
                        border: `1px solid ${theme("colors.gray.300")}`,
                        padding: `${theme("spacing.2")} ${theme("spacing.4")}`,
                        textAlign: "left",
                    },
                    td: {
                        border: `1px solid ${theme("colors.gray.300")}`,
                        padding: `${theme("spacing.2")} ${theme("spacing.4")}`,
                    },
                    // Horizontal rule
                    hr: {
                        borderColor: theme("colors.gray.200"),
                        borderTopWidth: "2px",
                        marginTop: theme("spacing.8"),
                        marginBottom: theme("spacing.8"),
                    },
                    // Paragraphs
                    p: {
                        color: theme("colors.gray.800"),
                        marginBottom: theme("spacing.4"),
                        lineHeight: theme("lineHeight.relaxed"),
                    },
                    // Links
                    a: {
                        color: theme("colors.blue.600"),
                        textDecoration: "underline",
                        transition: "color 0.2s",
                        "&:hover": {
                            color: theme("colors.blue.800"),
                        },
                    },
                },
            },
            invert: {
                css: {
                    color: theme("colors.gray.100"),
                    h1: {
                        color: theme("colors.gray.100"),
                        borderBottomColor: theme("colors.gray.700"),
                    },
                    h2: {
                        color: theme("colors.gray.100"),
                        borderBottomColor: theme("colors.gray.700"),
                    },
                    h3: { color: theme("colors.gray.100") },
                    strong: { color: theme("colors.gray.100") },
                    blockquote: {
                        borderLeftColor: theme("colors.blue.500"),
                        color: theme("colors.blue.200"),
                        backgroundColor: theme("colors.blue.900"),
                    },
                    code: {
                        backgroundColor: theme("colors.gray.800"),
                        color: theme("colors.pink.400"),
                    },
                    pre: {
                        backgroundColor: theme("colors.gray.900"),
                        color: theme("colors.gray.100"),
                    },
                    th: {
                        backgroundColor: theme("colors.gray.700"),
                        color: theme("colors.gray.100"),
                        borderColor: theme("colors.gray.600"),
                    },
                    td: { borderColor: theme("colors.gray.600") },
                    hr: { borderColor: theme("colors.gray.700") },
                    a: {
                        color: theme("colors.blue.400"),
                        "&:hover": { color: theme("colors.blue.300") },
                    },
                    p: { color: theme("colors.gray.300") },
                    ol: { color: theme("colors.gray.300") },
                    ul: { color: theme("colors.gray.300") },
                },
            },
        }),
    },
    plugins: [tailwindcssAnimate, tailwindcssTypography],
} satisfies Config;

export default config;
