{"python.defaultInterpreterPath": "./backend/.venv/bin/python", "python.terminal.activateEnvironment": true, "python.analysis.extraPaths": ["./backend"], "python.analysis.autoSearchPaths": true, "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "files.associations": {"*.py": "python"}, "python.envFile": "${workspaceFolder}/backend/.env", "[python]": {"editor.defaultFormatter": "charliermarsh.ruff", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "always"}}, "python.analysis.include": ["./backend/app/**"], "python.analysis.exclude": ["**/node_modules", "**/__pycache__", ".git"]}