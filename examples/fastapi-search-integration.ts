// src/examples/fastapi-search-integration.ts
// Example of how to integrate SearchSection with FastAPI backend
"use server";
import { z } from "zod";
import { calculateDuration } from "@/lib/utils";
import {
    itineraryFastAPI,
    prepareFastAPIData,
    withFastAPIFallback,
} from "@/app/fastapi-actions";
import { itineraryGenKit, prepareGenKitData } from "@/app/actions";
import { searchSchema } from "@/constants/itinerary";
/**
 * FastAPI-integrated version of the onSubmit function
 * This replaces the current Genkit-based implementation in SearchSection.tsx
 */
export async function onSubmitWithFastAPI(
    data: z.infer<typeof searchSchema>,
    setIsLoading: (loading: boolean) => void,
    setError: (error: string | null) => void,
    setItinerary: (itinerary: string | null) => void,
): Promise<void> {
    setIsLoading(true);
    setError(null);
    setItinerary(null);

    try {
        // Prepare FastAPI request data
        const fastAPIData = await prepareFastAPIData(data);
        const genKitData = await prepareGenKitData(data);

        // Use FastAPI with fallback to Genkit
        const result = await withFastAPIFallback(
            // FastAPI operation
            async () => {
                const itinerary = await itineraryFastAPI(fastAPIData);
                return { success: true, data: { answer: itinerary } };
            },
            // Fallback to Genkit operation
            async () => {
                const itinerary = await itineraryGenKit(genKitData);
                if (itinerary.success) {
                    // Map itinerary to answer to match the expected type
                    return {
                        success: true,
                        data: { answer: itinerary.data.itinerary },
                    };
                } else {
                    throw new Error(
                        itinerary.error || "Genkit fallback failed",
                    );
                }
            },
        );
        // If withFastAPIFallback resolves, it was successful.
        // Errors are thrown and caught by the outer catch block.
        setItinerary(result.data.answer);
    } catch (error) {
        console.error("Itinerary generation error:", error);
        const errorMessage =
            error instanceof Error
                ? error.message
                : "An unexpected error occurred while generating your itinerary. Please try again.";
        setError(errorMessage);
    } finally {
        setIsLoading(false);
    }
}

/**
 * Simplified FastAPI-only version (no fallback)
 * Use this if you want to fully migrate to FastAPI
 */
export async function onSubmitFastAPIOnly(
    data: z.infer<typeof searchSchema>,
    setIsLoading: (loading: boolean) => void,
    setError: (error: string | null) => void,
    setItinerary: (itinerary: string | null) => void,
): Promise<void> {
    setIsLoading(true);
    setError(null);
    setItinerary(null);

    try {
        // Calculate duration from date range
        const duration = calculateDuration(data.date?.from, data.date?.to);

        // Convert interests string to array
        const interestsArray = data.interests
            .split(",")
            .map((interest) => interest.trim())
            .filter((interest) => interest.length > 0);

        // Call FastAPI directly
        const itinerary = await itineraryFastAPI({
            destination: "Bali, Indonesia",
            duration: duration,
            interests: interestsArray,
        });

        setItinerary(itinerary);
    } catch (error) {
        console.error("FastAPI itinerary generation error:", error);
        const errorMessage =
            error instanceof Error
                ? error.message
                : "Failed to generate itinerary. Please check your connection and try again.";
        setError(errorMessage);
    } finally {
        setIsLoading(false);
    }
}
