# BaliBlissed AI Backend API Documentation

This document provides comprehensive guidance on how to integrate and use the BaliBlissed AI Backend API endpoints in your Next.js project.

## 🚀 Getting Started

### Base URL

- **Development**: `http://localhost:8000`
- **Production**: Set via `PRODUCTION_FRONTEND_URL` environment variable

### Authentication

Currently, no authentication is required for the API endpoints.

### Content Type

All POST requests should use `Content-Type: application/json`

## 📋 Available Endpoints

### Health Check Endpoints

#### GET `/`

Basic health check endpoint.

**Response:**

```json
{
  "message": "BaliBlissed AI Backend is running!"
}
```

#### GET `/health`

Detailed health status with system information.

**Response:**

```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "environment": "development"
}
```

### AI-Powered Endpoints

#### POST `/api/suggest-itinerary`

Generate personalized travel itineraries for Bali.

**Request Body:**

```json
{
  "destination": "Bali, Indonesia",
  "duration": 7,
  "interests": ["beaches", "temples", "culture", "food"]
}
```

**Response:**

```json
{
  "itinerary": "Day 1: Arrival in Denpasar...\nDay 2: Explore Ubud..."
}
```

#### POST `/api/answer-query`

Process travel-related questions with AI assistance.

**Request Body:**

```json
{
  "query": "What are the best beaches in Bali?",
  "history": [
    {
      "role": "user",
      "parts": [{"text": "Previous question"}]
    },
    {
      "role": "assistant", 
      "parts": [{"text": "Previous response"}]
    }
  ]
}
```

**Response:**

```json
{
  "answer": "Bali offers many stunning beaches including Kuta Beach..."
}
```

#### POST `/api/handle-contact-inquiry`

Analyze and categorize contact form submissions.

**Request Body:**

```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "message": "I'm interested in a 10-day Bali trip for my family."
}
```

**Response:**

```json
{
  "analysis": {
    "category": "Trip Planning",
    "sentiment": "Positive",
    "urgency": "Medium",
    "suggested_response": "Thank you for your interest in our Bali packages..."
  }
}
```

## 🏗️ Current Architecture Overview

The BaliBlissed project currently uses a **dual architecture approach**:

### Primary Implementation: Genkit-based Actions

- **Location**: `src/app/actions.ts`
- **Technology**: Google Genkit framework with TypeScript
- **Usage**: Direct server actions in Next.js components
- **Benefits**: Integrated development, type safety, simpler deployment

### Secondary Implementation: FastAPI Backend

- **Location**: `backend/main.py` + `src/app/fastapi-actions.ts`
- **Technology**: Python FastAPI + TypeScript client
- **Usage**: HTTP API calls to separate backend service
- **Benefits**: Independent scaling, advanced AI features, multi-client support

## 🔄 Current Implementation Analysis

### SearchSection.tsx Implementation

**Current Genkit Usage:**

```typescript
// From src/components/home/<USER>
import { handleItineraryRequest } from "@/app/actions";

// Form submission with budget slider and date picker
const result = await handleItineraryRequest({
  interests: data.interests,
  travelDates: travelDates,
  budget: formatCurrency(data.budget),
});
```

**Key Features:**

- Budget slider: `MIN: 300, MAX: 10000, STEP: 100, DEFAULT: 300`
- Date range picker with validation
- Interest selection with comma-separated values
- WhatsApp integration for sharing results

### ExpandBot.tsx Implementation

**Current Genkit Usage:**

```typescript
// From src/components/ExpandBot.tsx
import { useChatMessages } from "@/hooks/use-chat-messages";

// Chat interface with expandable dock
const { messages, isLoading, sendMessage, clearHistory } = useChatMessages();
```

**Key Features:**

- Expandable dock interface with smooth animations
- Message history with auto-scroll
- Loading states with typing indicators
- Error handling with user feedback

### Existing FastAPI Integration

**Already Available:**

```typescript
// From src/app/fastapi-actions.ts
export async function getItineraryFromFastAPI(data: ItineraryRequest): Promise<string> {
  const endpoint = `${FASTAPI_BASE_URL}/api/suggest-itinerary`;
  // Implementation already exists
}
```

## 🔧 FastAPI Integration Guide

### 1. Environment Setup

```bash
# .env.local
FASTAPI_URL=http://127.0.0.1:8000
# For production:
# FASTAPI_URL=https://your-fastapi-domain.com
```

### 2. Enhanced FastAPI Actions

The existing `fastapi-actions.ts` can be extended with additional endpoints:

```typescript
// Extended FastAPI integration
export async function answerQueryWithFastAPI(data: QueryRequest): Promise<string>
export async function analyzeContactWithFastAPI(data: ContactInquiryRequest): Promise<any>
```

### 3. Migration Examples

#### Itinerary Generation Migration

**Before (Current Genkit):**

```typescript
const result = await handleItineraryRequest({
  interests: data.interests,
  travelDates: travelDates,
  budget: formatCurrency(data.budget),
});
```

**After (FastAPI):**

```typescript
const duration = calculateDuration(data.date?.from, data.date?.to);
const itinerary = await getItineraryFromFastAPI({
  destination: "Bali, Indonesia",
  duration: duration,
  interests: data.interests.split(',').map(i => i.trim()),
});
```

#### Chat Migration

**Before (Current):**

```typescript
import { useChatMessages } from "@/hooks/use-chat-messages";
```

**After (FastAPI):**

```typescript
import { useFastAPIChat } from "@/hooks/use-fastapi-chat";
```

## 📊 Comparison: Current vs FastAPI

| Feature            | Current (Genkit)          | FastAPI Alternative                  |
| ------------------ | ------------------------- | ------------------------------------ |
| **Architecture**   | TypeScript server actions | Python FastAPI backend               |
| **AI Integration** | Google Genkit framework   | Direct Google Gemini API             |
| **Type Safety**    | Full TypeScript support   | TypeScript frontend + Python backend |
| **Performance**    | Excellent (same runtime)  | Good (network overhead)              |
| **Scalability**    | Limited by Next.js server | Independent scaling                  |
| **Deployment**     | Single Next.js deployment | Separate backend deployment          |
| **Development**    | Integrated development    | Separate backend development         |
| **Error Handling** | TypeScript error handling | HTTP status codes + custom errors    |
| **Chat History**   | Simple object passing     | Structured conversation format       |
| **Validation**     | Zod schemas               | Pydantic models                      |

## 🎯 Recommendations

### When to Use FastAPI Backend

**Use FastAPI When:**

- You need independent backend scaling
- Planning to add mobile app support
- Require advanced AI model customization
- Need detailed analytics and monitoring
- Want to separate AI processing from web serving

### When to Keep Genkit Implementation

**Keep Genkit When:**

- Simple deployment requirements
- Small to medium scale applications
- Rapid prototyping and development
- Team prefers TypeScript-only stack
- Cost optimization is priority

**Recommendation**: Start with the current Genkit implementation for development and consider FastAPI migration when you need:

- Independent backend scaling
- Multi-client support (mobile apps)
- Advanced AI model customization
- Detailed analytics and monitoring

Both approaches are production-ready and can coexist during a gradual migration if needed.
