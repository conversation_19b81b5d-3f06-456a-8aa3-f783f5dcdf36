# Docker Setup Guide for BaliBlissed Backend

Complete Docker configuration for the BaliBlissed FastAPI backend with Redis caching support.

## 🚀 Quick Start

### Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- Git

### 1. <PERSON>lone and Setup

```bash
git clone <repository-url>
cd BaliBlissed_NextJs
```

### 2. Environment Configuration

**For Development:**

```bash
cp .env.development.example .env
```

**For Production:**

```bash
cp .env.production.example .env.production
```

### 3. Update Environment Variables

Edit your `.env` file and update:

- `GEMINI_API_KEY` - Your Google Gemini API key
- `MAIL_USERNAME` - Your email address
- `MAIL_PASSWORD` - Your email app password
- `REDIS_PASSWORD` - Redis password (production only)

### 4. Start Services

**Development:**

```bash
docker-compose up --build
```

**Production:**

```bash
docker-compose --env-file .env.production up -d --build
```

### 5. Verify Setup

- Backend API: <http://localhost:8000>
- API Documentation: <http://localhost:8000/docs>
- Health Check: <http://localhost:8000/health>
- Redis Commander (dev): <http://localhost:8081>

## 📋 Services Overview

### Backend Service

- **Image**: Custom FastAPI application
- **Port**: 8000
- **Features**: Hot reload (dev), optimized workers (prod)
- **Health Check**: `/health` endpoint

### Redis Service

- **Image**: redis:7-alpine
- **Port**: 6379
- **Features**: Persistent storage, custom configuration
- **Health Check**: `redis-cli ping`

### Redis Commander (Development Only)

- **Image**: rediscommander/redis-commander
- **Port**: 8081
- **Purpose**: Redis database management UI

## 🔧 Configuration

### Docker Compose Profiles

**Development Profile:**

```bash
docker-compose --profile development up
```

- Includes Redis Commander
- Hot reload enabled
- Debug logging

**Production Profile:**

```bash
docker-compose --profile production up
```

- No Redis Commander
- Optimized for performance
- Security hardened

### Environment Variables

| Variable         | Description          | Default       | Required |
| ---------------- | -------------------- | ------------- | -------- |
| `REDIS_ENABLED`  | Enable Redis caching | `true`        | No       |
| `REDIS_HOST`     | Redis hostname       | `redis`       | No       |
| `REDIS_PORT`     | Redis port           | `6379`        | No       |
| `REDIS_PASSWORD` | Redis password       | ``            | No       |
| `BUILD_TARGET`   | Docker build target  | `development` | No       |
| `BACKEND_PORT`   | Backend port mapping | `8000`        | No       |

### Volume Mounts

- `redis_data`: Redis persistent storage
- `backend_logs`: Application logs
- `./backend:/app`: Source code (development only)

## 🛠️ Development Workflow

### Starting Development Environment

```bash
# Start all services
docker-compose up

# Start specific service
docker-compose up redis

# Rebuild and start
docker-compose up --build

# Run in background
docker-compose up -d
```

### Viewing Logs

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f redis

# Last 100 lines
docker-compose logs --tail=100 backend
```

### Executing Commands

```bash
# Backend shell
docker-compose exec backend bash

# Redis CLI
docker-compose exec redis redis-cli

# Run tests
docker-compose exec backend python -m pytest
```

### Hot Reload

The development setup includes hot reload:

- Code changes are automatically detected
- Server restarts automatically
- No need to rebuild container

## 🚀 Production Deployment

### Production Checklist

- [ ] Set `ENVIRONMENT=production`
- [ ] Configure strong `REDIS_PASSWORD`
- [ ] Set resource limits
- [ ] Enable SSL/TLS
- [ ] Configure monitoring
- [ ] Set up backups

### Production Commands

```bash
# Start production environment
docker-compose --env-file .env.production up -d

# Scale backend service
docker-compose up -d --scale backend=3

# Update services
docker-compose pull
docker-compose up -d --no-deps backend

# Backup Redis data
docker-compose exec redis redis-cli BGSAVE
```

### Resource Limits

Production configuration includes resource limits:

- Backend: 2GB RAM, 2 CPU cores
- Redis: 1GB RAM, 1 CPU core

Adjust in `.env.production`:

```env
BACKEND_MEMORY_LIMIT=2G
BACKEND_CPU_LIMIT=2.0
REDIS_MEMORY_LIMIT=1G
REDIS_CPU_LIMIT=1.0
```

## 🔍 Monitoring and Health Checks

### Health Check Endpoints

- **Backend**: `GET /health`
- **Redis**: `redis-cli ping`

### Monitoring Commands

```bash
# Service status
docker-compose ps

# Resource usage
docker stats

# Health status
curl http://localhost:8000/health

# Redis info
docker-compose exec redis redis-cli info
```

## 🐛 Troubleshooting

### Common Issues

## **1. Redis Connection Failed**

```bash
# Check Redis status
docker-compose ps redis

# Check Redis logs
docker-compose logs redis

# Test Redis connection
docker-compose exec redis redis-cli ping
```

## **2. Backend Won't Start**

```bash
# Check backend logs
docker-compose logs backend

# Check environment variables
docker-compose exec backend env | grep REDIS

# Restart backend
docker-compose restart backend
```

## **3. Port Already in Use**

```bash
# Check what's using the port
lsof -i :8000
lsof -i :6379

# Change ports in .env
BACKEND_PORT=8001
REDIS_PORT=6380
```

## **4. Permission Issues**

```bash
# Fix file permissions
sudo chown -R $USER:$USER ./data
sudo chown -R $USER:$USER ./logs

# Recreate volumes
docker-compose down -v
docker-compose up
```

### Debug Mode

Enable debug logging:

```env
LOG_LEVEL=DEBUG
DEBUG=true
```

### Reset Everything

```bash
# Stop and remove everything
docker-compose down -v --remove-orphans

# Remove images
docker-compose down --rmi all

# Clean rebuild
docker-compose build --no-cache
docker-compose up
```

## 📁 Directory Structure

```plain text
BaliBlissed_NextJs/
├── docker-compose.yml          # Main Docker Compose configuration
├── .env.development.example    # Development environment template
├── .env.production.example     # Production environment template
├── backend/
│   ├── Dockerfile             # Backend container definition
│   └── ...
├── redis/
│   └── redis.conf             # Redis configuration
├── data/
│   └── redis/                 # Redis persistent data
└── logs/                      # Application logs
```

## 🔗 Related Documentation

- [Backend API Documentation](./API_USAGE.md)
- [FastAPI Integration Guide](./FASTAPI_INTEGRATION_GUIDE.md)
- [Performance Analysis](./PERFORMANCE_ANALYSIS_REPORT.md)
- [Quick Start Guide](../backend/docs/QUICK_START.md)
