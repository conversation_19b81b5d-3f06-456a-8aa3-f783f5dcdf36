# FastAPI Integration Guide for Itinerary Requests

This guide shows you how to integrate itinerary requests with the FastAPI backend, including the missing `calculateDuration` function.

## 🔧 Step 1: Add the calculateDuration Function

The `calculateDuration` function has been added to `src/lib/utils.ts`:

```typescript
/**
 * Calculate the duration in days between two dates
 * @param fromDate - Start date (optional)
 * @param toDate - End date (optional)
 * @returns Number of days between dates, defaults to 7 if no dates provided
 */
export function calculateDuration(fromDate?: Date, toDate?: Date): number {
    // Default duration if no dates provided
    if (!fromDate) {
        return 7;
    }

    // Single date selected - assume 1 day trip
    if (!toDate) {
        return 1;
    }

    // Calculate difference in days
    const timeDifference = toDate.getTime() - fromDate.getTime();
    const daysDifference = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));

    // Ensure minimum 1 day duration
    return Math.max(1, daysDifference);
}
```

## 🚀 Step 2: Integration Options

### Option A: Gradual Migration with Fallback (Recommended)

Replace the `onSubmit` function in `SearchSection.tsx`:

```typescript
// Add these imports at the top
import { calculateDuration } from "@/lib/utils";
import { getItineraryFromFastAPI, withFastAPIFallback } from "@/app/fastapi-actions";

// Replace the existing onSubmit function
async function onSubmit(data: z.infer<typeof searchSchema>): Promise<void> {
    setIsLoading(true);
    setError(null);
    setItinerary(null);

    try {
        // Calculate duration from date range
        const duration = calculateDuration(data.date?.from, data.date?.to);

        // Convert interests string to array
        const interestsArray = data.interests
            .split(',')
            .map(interest => interest.trim())
            .filter(interest => interest.length > 0);

        // Use FastAPI with fallback to Genkit
        const result = await withFastAPIFallback(
            // FastAPI operation
            async () => {
                const itinerary = await getItineraryFromFastAPI({
                    destination: "Bali, Indonesia",
                    duration: duration,
                    interests: interestsArray,
                });
                return { success: true, data: { answer: itinerary } };
            },
            // Fallback to current Genkit implementation
            async () => {
                let travelDates = "any time";
                if (data.date?.from) {
                    if (data.date.to) {
                        travelDates = `${format(data.date.from, "yyyy-MM-dd")} to ${format(data.date.to, "yyyy-MM-dd")}`;
                    } else {
                        travelDates = format(data.date.from, "yyyy-MM-dd");
                    }
                }

                return await handleItineraryRequest({
                    interests: data.interests,
                    travelDates: travelDates,
                    budget: formatCurrency(data.budget),
                });
            }
        );

        if (result.success && result.data) {
            setItinerary(result.data.answer);
        } else {
            throw new Error(result.error || "Failed to generate itinerary");
        }

    } catch (error) {
        console.error("Itinerary generation error:", error);
        const errorMessage = error instanceof Error 
            ? error.message 
            : "An unexpected error occurred while generating your itinerary. Please try again.";
        setError(errorMessage);
    } finally {
        setIsLoading(false);
    }
}
```

### Option B: FastAPI Only (Full Migration)

For a complete migration to FastAPI:

```typescript
// Add these imports
import { calculateDuration } from "@/lib/utils";
import { getItineraryFromFastAPI } from "@/app/fastapi-actions";

// Replace the onSubmit function
async function onSubmit(data: z.infer<typeof searchSchema>): Promise<void> {
    setIsLoading(true);
    setError(null);
    setItinerary(null);

    try {
        const duration = calculateDuration(data.date?.from, data.date?.to);
        
        const interestsArray = data.interests
            .split(',')
            .map(interest => interest.trim())
            .filter(interest => interest.length > 0);

        const itinerary = await getItineraryFromFastAPI({
            destination: "Bali, Indonesia",
            duration: duration,
            interests: interestsArray,
        });

        setItinerary(itinerary);

    } catch (error) {
        console.error("FastAPI itinerary generation error:", error);
        const errorMessage = error instanceof Error 
            ? error.message 
            : "Failed to generate itinerary. Please check your connection and try again.";
        setError(errorMessage);
    } finally {
        setIsLoading(false);
    }
}
```

## 🔍 Key Differences

| Aspect             | Current (Genkit)                           | FastAPI Integration                              |
| ------------------ | ------------------------------------------ | ------------------------------------------------ |
| **Date Handling**  | String format ("yyyy-MM-dd to yyyy-MM-dd") | Duration in days (number)                        |
| **Interests**      | Comma-separated string                     | Array of strings                                 |
| **Budget**         | Formatted currency string                  | Not used in FastAPI (backend focused on content) |
| **Response**       | `{ success, data: { answer } }`            | Direct string response                           |
| **Error Handling** | Wrapped in success/error object            | Direct error throwing                            |

## 🛠️ Environment Setup

Make sure your `.env.local` includes:

```bash
FASTAPI_URL=http://127.0.0.1:8000
# For production:
# FASTAPI_URL=https://your-fastapi-domain.com
```

## 🧪 Testing the Integration

1. **Start the FastAPI backend** (if not already running):

   ```bash
   cd backend
   python main.py
   ```

2. **Test the calculateDuration function**:

   ```typescript
   import { calculateDuration } from "@/lib/utils";
   
   // Test cases
   console.log(calculateDuration()); // 7 (default)
   console.log(calculateDuration(new Date())); // 1 (single date)
   console.log(calculateDuration(new Date(), new Date(Date.now() + 5 * 24 * 60 * 60 * 1000))); // 5 (5 days)
   ```

3. **Test the FastAPI integration** by submitting the form in SearchSection

## 📝 Benefits of FastAPI Integration

- **Independent Scaling**: Backend can scale separately from frontend
- **Advanced AI Features**: More control over AI model parameters
- **Multi-client Support**: Same backend can serve web, mobile, etc.
- **Better Analytics**: Centralized logging and monitoring
- **Flexible Deployment**: Backend can be deployed independently

## 🔄 Migration Strategy

1. **Phase 1**: Implement with fallback (Option A) - ensures no downtime
2. **Phase 2**: Monitor FastAPI performance and reliability
3. **Phase 3**: Gradually remove fallback once confident in FastAPI stability
4. **Phase 4**: Full migration to FastAPI-only implementation

This approach ensures a smooth transition while maintaining system reliability.
